# region =======================================备注说明
"""
备注说明：
    官方文档：https://docs.backpack.exchange/#section/Introduction
    官方SDK：https://github.com/sndmndss/bpx-py  

    pip install bpx-py
"""
# endregion =======================================备注说明


# region =======================================import
from traceback import format_exc
from math import log
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .ExchangeAdapter import ExchangeAdapter
from .api_config import backpack_api_config

from bpx.public import Public
from bpx.account import Account
# from bpx.constants.enums import OrderTypeEnum
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from time import time
from datetime import timedelta

# endregion =======================================import   



class BackpackAdapter(ExchangeAdapter):
    # BASE_URL and BASE_URL_WAPI are not needed as SDK handles endpoints
    # BASE_URL = "https://api.backpack.exchange/api/v1"
    # BASE_URL_WAPI = "https://api.backpack.exchange/wapi/v1"

    def __init__(self, account='<EMAIL>'):
        self._api_config = backpack_api_config.api_config
        self.account_name = account # Renamed from self.account to avoid conflict with SDK client
        self.exchange_name = 'backpack'
        
        self._api_key = None
        self._secret_key = None # Store the raw secret key for the SDK

        # SDK Clients
        self.public_client = Public()
        self.account_client = None # Initialized if keys are found

        config = self._api_config.get(self.account_name)
        if config and config.get('api_key') and config.get('secret_key'):
            try:
                self._api_key = config['api_key'] # This is the public API key string
                self._secret_key = config['secret_key'] # This is the raw secret key string

                # Initialize SDK Account client
                # The SDK handles key types and signing internally.
                # Default window is 5000, can be customized if needed, e.g. window=config.get('window', 5000)
                self.account_client = Account(
                    public_key=self._api_key,
                    secret_key=self._secret_key
                )
                # print(f"Backpack SDK Account client initialized for {self.account_name}.")

            except:
                print(f"Error initializing Backpack SDK Account client for account {self.account_name}: {e}")
                print(format_exc())
                self.account_client = None
        else:
            print(f"API key or secret key not found or invalid for account: {self.account_name}. Only public endpoints will work.")

    # _sign_request method is no longer needed as SDK handles signing.
    # _send_request method is no longer needed as SDK handles requests.
    # _send_request2 method is no longer needed as SDK handles requests.

    # region =======================================spot
    # 获取当前最新价格
    def get_spot_last_price(self, symbol: str) -> Optional[float]:
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        {
            firstPrice: "103694.8",
            high: "103720.2",
            lastPrice: "103371.7",
            low: "102686.4",
            priceChange: "-323.1",
            priceChangePercent: "-0.003116",
            quoteVolume: "4742444.112553",
            symbol: "BTC_USDC",
            trades: "15562",
            volume: "45.90877"
            }
        """
        try:
            return self.public_client.get_ticker(symbol=symbol.upper() + '_USDC')['lastPrice']
        except:
            print(format_exc())
            return None

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol: str) -> Optional[float]:
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        """
        try:
            return self.public_client.get_depth(symbol=symbol.upper() + '_USDC')['bids'][-1][0]
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol: str) -> Optional[float]:
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        """
        try:
            return self.public_client.get_depth(symbol=symbol.upper() + '_USDC')['asks'][0][0]
        except:
            print(format_exc()) 
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol: str) -> Optional[Dict[str, List[str]]]:
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        """
        try:
            info = self.public_client.get_depth(symbol=symbol.upper() + '_USDC')
            return {
                'bid': [info['bids'][-1][0], info['bids'][-1][1]],
                'ask': [info['asks'][0][0], info['asks'][0][1]]
            }
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_spot_orderbook(self, symbol: str, limit: int = 5) -> Optional[Dict[str, Any]]:
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            info =  self.public_client.get_depth(symbol=symbol.upper() + '_USDC')
            return {
                'bids': info['bids'][-limit:][::-1],
                'asks': info['asks'][:limit]
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self): 
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        :param symbol:
        :return: 
        {
            baseSymbol: "SOL",
            createdAt: "2025-01-21T06:34:54.691858",
            filters: {
                price: {
                borrowEntryFeeMaxMultiplier: None,
                borrowEntryFeeMinMultiplier: None,
                maxImpactMultiplier: "1.03",
                maxMultiplier: "1.25",
                maxPrice: None,
                meanMarkPriceBand: { maxMultiplier: "1.15", minMultiplier: "0.9" },
                meanPremiumBand: None,
                minImpactMultiplier: "0.97",
                minMultiplier: "0.75",
                minPrice: "0.01",
                tickSize: "0.01"
                },
                quantity: { maxQuantity: None, minQuantity: "0.01", stepSize: "0.01" }
            },
            fundingInterval: 28800000,
            fundingRateLowerBound: None,
            fundingRateUpperBound: None,
            imfFunction: None,
            marketType: "SPOT",
            mmfFunction: None,
            openInterestLimit: "0",
            orderBookState: "Open",
            quoteSymbol: "USDC",
            symbol: "SOL_USDC"
            }
        """
        try:
            info = self.public_client.get_market()
            for item in info:
                if item['baseSymbol'] == symbol.upper():
                    return item
            return None
        except:
            print(format_exc())
            return None
        
    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol: str) -> Optional[int]:
        """
        :param symbol:
        :return:
        """
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            if instrument_info and instrument_info.get('filters', {}).get('price', {}).get('tickSize'):
                tick_size_str = instrument_info['filters']['price']['tickSize']
                return int(round(log(1 / float(tick_size_str), 10), 0))
            return None
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol: str) -> Optional[int]:
        """
        :param symbol:
        :return:
        """
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            if instrument_info and instrument_info.get('filters', {}).get('quantity', {}).get('stepSize'):
                step_size_str = instrument_info['filters']['quantity']['stepSize']
                return int(round(log(1 / float(step_size_str), 10), 0))
            return None
        except:
            print(f"Error in get_spot_order_amount_tick_size for {symbol}: {e}")
            print(format_exc())
            return None

    # TODO：获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        pass

    # TODO：获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        pass

    # 获取最小下单数量
    def get_spot_min_amount(self, symbol: str) -> Optional[float]:
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            if instrument_info and instrument_info.get('filters', {}).get('quantity', {}).get('minQuantity'):
                min_quantity_str = instrument_info['filters']['quantity']['minQuantity']
                return float(min_quantity_str)
            return None
        except:
            print(format_exc())
            return None
        
    # 获取现货币对(默认usdc)
    def get_spot_instruments_symbols(self, base_symbol: str = 'usdc') -> Optional[List[str]]:
        """
        :return:
        """
        try:
            markets_data = self.public_client.get_markets()
            symbols = []
            if markets_data:
                for item in markets_data:
                    if item['marketType'] == 'SPOT' and item['quoteSymbol'] == base_symbol.upper():
                        symbols.append(item['baseSymbol'])
                return symbols
            else:
                return None
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol: str) -> Optional[float]:
        """
        :param symbol:
        :return:
        """
        try:
            all_balances = self.get_spot_account()
            if all_balances and symbol.lower() in all_balances:
                return float(all_balances[symbol.lower()])
            return 0.0
        except:
            print(format_exc())
            return None
        
    # 获取spot账户余额(多币种)
    def get_spot_account(self) -> Optional[Dict[str, str]]:
        """
        :return: 
        {
            IO: { available: "2", locked: "0", staked: "0" },
            JTO: { available: "0.085215", locked: "0", staked: "0" },
            KMNO: { available: "0.001142", locked: "0", staked: "0" },
            POINTS: { available: "66.8", locked: "0", staked: "0" },
            SOL: { available: "20.491069", locked: "0", staked: "0" },
            USDC: { available: "58.5167194", locked: "0", staked: "0" },
            USDT: { available: "0", locked: "0", staked: "0" },
            W: { available: "0", locked: "0", staked: "0" }
            }
        """
        try:
            info = self.account_client.get_balances()
            # 去除points(积分)
            # info.pop('POINTS')
            if info:
                return {
                    asset.lower(): details['available']
                    for asset, details in info.items() if details['available'] != '0'
                }
            return None
        except:
            print(format_exc())
            return None

    # 获取spot账户挂单
    def get_spot_open_order(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        获取spot账户挂单
        :param symbol: if None, gets all open orders. If specified, filters by symbol.
        :return:
        [
            {
                clientId: None,
                createdAt: *************,
                executedQuantity: "0",
                executedQuoteQuantity: "0",
                id: "114527481836208128",
                orderType: "Limit",
                postOnly: False,
                price: "3.5",
                quantity: "10",
                reduceOnly: None,
                relatedOrderId: None,
                selfTradePrevention: "RejectTaker",
                side: "Bid",
                status: "New",
                stopLossLimitPrice: None,
                stopLossTriggerBy: None,
                stopLossTriggerPrice: None,
                symbol: "SUI_USDC",
                takeProfitLimitPrice: None,
                takeProfitTriggerBy: None,
                takeProfitTriggerPrice: None,
                timeInForce: "GTC",
                triggerBy: None,
                triggerPrice: None,
                triggerQuantity: None,
                triggeredAt: None
            }
            ]
        """
        try:
            info = self.account_client.get_open_orders()
            if info:
                if symbol:
                    return [{
                        'order_id': item['id'],
                        'symbol': item['symbol'].replace('_USDC', ''),
                        'direction': 'buy' if item['side'] == 'Bid' else 'sell',
                        'amount': item['quantity'],
                        'price': item['price'],
                        'order_type': item['orderType'],
                        'average_price': '',
                        'remain_amount': float(item['quantity']) - float(item['executedQuantity']),
                    } for item in info if item['symbol'] == f"{symbol.upper()}_USDC"]
                else:
                    return [{
                        'order_id': item['id'],
                        'symbol': item['symbol'].replace('_USDC', ''),
                        'direction': 'buy' if item['side'] == 'Bid' else 'sell',
                        'amount': item['quantity'],
                        'price': item['price'],
                        'order_type': item['orderType'],
                        'average_price': '',
                        'remain_amount': float(item['quantity']) - float(item['executedQuantity']),
                    } for item in info]
            return None
        except:
            print(f"Error in get_spot_open_order for {symbol}: {e}")
            print(format_exc())
            return None

    # 下单
    def place_spot_order(self, symbol: str, direction: str, amount: Optional[Union[float, str]] = None, 
                         price: Optional[Union[float, str]] = None, order_type: str = 'limit', 
                         params={'quoteOrderQty': None}) -> Optional[Dict[str, Any]]:
        """
        :param params: e.g. {'quoteQuantity': '10.0'} for market buy
        :return:
        {
            clientId: None,
            createdAt: 1747552544327,
            executedQuantity: "0",
            executedQuoteQuantity: "0",
            id: "114527603545014272",
            orderType: "Limit",
            postOnly: False,
            price: "171",
            quantity: "0.1",
            reduceOnly: None,
            relatedOrderId: None,
            selfTradePrevention: "RejectTaker",
            side: "Ask",
            status: "New",
            stopLossLimitPrice: None,
            stopLossTriggerBy: None,
            stopLossTriggerPrice: None,
            symbol: "SOL_USDC",
            takeProfitLimitPrice: None,
            takeProfitTriggerBy: None,
            takeProfitTriggerPrice: None,
            timeInForce: "GTC",
            triggerBy: None,
            triggerPrice: None,
            triggerQuantity: None,
            triggeredAt: None
            }
        """
        try:
            if order_type == 'limit':
                order_info = self.account_client.execute_order(
                    symbol=symbol.upper() + '_USDC', 
                    order_type='Limit' if order_type == 'limit' else 'Market',
                    side='Bid' if direction == 'buy' else 'Ask',
                    quantity=amount,
                    price=price,
                )
            else:
                if not params['quoteOrderQty']:
                    order_info = self.account_client.execute_order(
                        symbol=symbol.upper() + '_USDC', 
                        order_type='Market',
                        side='Bid' if direction == 'buy' else 'Ask',
                        quantity=amount,
                        )
                else:
                    order_info = self.account_client.execute_order(
                        symbol=symbol.upper() + '_USDC', 
                        order_type='Market',
                        side='Bid' if direction == 'buy' else 'Ask',
                        quote_quantity=params['quoteOrderQty'],
                    )
            if order_info:
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['id'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount if order_type == 'limit' else order_info['quantity'],
                    'price': price if order_type == 'limit' else '',
                    'order_type': order_type,
                }
            else:
                return None
        except:
            print(format_exc())
            return None

    # TODO：下单 spot下单(适用于交叉币种,比如eth/btc, backpack暂时没有市价单)
    def place_spot_order2(self, symbol, base_symbol, direction, amount, price='', order_type='limit'):
        # This would be similar to place_spot_order, just construct the market_symbol differently
        # e.g., market_symbol = f"{symbol.upper()}_{base_symbol.upper()}"
        # Ensure such markets exist and are supported.
        print("place_spot_order2 for cross-pairs needs verification of market symbol format and availability.")
        return None

    # 获取订单信息
    def get_spot_order_info(self, symbol: str, order_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        :param symbol:
        :param order_id:
        :return:
            {
                clientId: None,
                createdAt: "2025-05-18T06:48:47.181",
                executedQuantity: "0",
                executedQuoteQuantity: "0",
                expiryReason: None,
                id: "114527497563734016",
                orderType: "Limit",
                postOnly: False,
                price: "171",
                quantity: "0.1",
                quoteQuantity: "17.1",
                selfTradePrevention: "RejectTaker",
                side: "Ask",
                status: "New",
                stopLossLimitPrice: None,
                stopLossTriggerBy: None,
                stopLossTriggerPrice: None,
                symbol: "SOL_USDC",
                takeProfitLimitPrice: None,
                takeProfitTriggerBy: None,
                takeProfitTriggerPrice: None,
                timeInForce: "GTC",
                triggerBy: None,
                triggerPrice: None,
                triggerQuantity: None
            }
        """
        try:
            info = self.account_client.get_order_history(symbol=f"{symbol.upper()}_USDC", order_id=order_id)
            if info:
                info = info[0]
                return {
                    'exchange': self.exchange_name,
                    'order_id': info['id'],
                    'symbol': symbol,
                    'direction': 'buy' if info['side'] == 'Bid' else 'sell',
                    'order_type': info['orderType'].lower(),
                    'amount': info['quantity'],
                    'price': info['price'],
                    'average_price': '',
                    'remain_amount': float(info['quantity']) - float(info['executedQuantity']),
                    'fee': '',
                }
            return None
        except:
            print(format_exc())
            return None

    # TODO：获取订单信息2
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        pass

    # 取消订单
    def cancel_spot_order(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]:
        """
        :param symbol:
        :param order_id:
        :return:
        {
            clientId: None,
            createdAt: *************,
            executedQuantity: "0",
            executedQuoteQuantity: "0",
            id: "114527481836208128",
            orderType: "Limit",
            postOnly: False,
            price: "3.5",
            quantity: "10",
            reduceOnly: None,
            relatedOrderId: None,
            selfTradePrevention: "RejectTaker",
            side: "Bid",
            status: "Cancelled",
            stopLossLimitPrice: None,
            stopLossTriggerBy: None,
            stopLossTriggerPrice: None,
            symbol: "SUI_USDC",
            takeProfitLimitPrice: None,
            takeProfitTriggerBy: None,
            takeProfitTriggerPrice: None,
            timeInForce: "GTC",
            triggerBy: None,
            triggerPrice: None,
            triggerQuantity: None,
            triggeredAt: None
            }
        """
        try:
            info = self.account_client.cancel_order(symbol=f"{symbol.upper()}_USDC", order_id=order_id)
            if info:
                return {
                    'order_id': info['id'],
                    'symbol': symbol,
                    'status': 'canceled' if info['status'] == 'Cancelled' else 'failed'
                }
            return None
        except:
            print(format_exc())
            return None
    # endregion

    # TODO：交割合约
    # region =======================================future (Placeholder - Needs SDK mapping for futures if distinct from PERP)
    # Assuming "future" functions are intended for PERP markets if applicable,
    # otherwise they need specific SDK support for distinct futures products.

    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        # This logic is specific to an API that lists products with 'alias' and 'underlying_index'.
        # The current SDK uses get_markets() which lists spot/perp symbols.
        # This method might need to be re-thought based on how futures/contracts are represented in SDK.
        # For PERP, the symbol is just SYMBOL_USDC_PERP.
        print("get_instrument_id: SDK mapping for distinct futures products needs clarification.")
        if contract_type: # If a specific contract type is asked, it's likely a PERP symbol
            return f"{symbol.upper()}_USDC_PERP" # Simplistic assumption
        return None


    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        # If this means PERP:
        try:
            # instrument_id = self.get_instrument_id(symbol, margin_type, contract_type) # This is complex
            market_symbol = f"{symbol.upper()}_USDC_PERP" # Assuming PERP for "future"
            depth_data: SDKDepth = self.public_client.get_depth(symbol=market_symbol)
            if depth_data and depth_data.bids:
                return float(depth_data.bids[0][0])
            return None
        except:
            print(f"Error in get_future_buy1 (as PERP) for {symbol}: {e}")
            print(format_exc())
            return None

    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        # If this means PERP:
        try:
            market_symbol = f"{symbol.upper()}_USDC_PERP" # Assuming PERP for "future"
            depth_data: SDKDepth = self.public_client.get_depth(symbol=market_symbol)
            if depth_data and depth_data.asks:
                return float(depth_data.asks[0][0])
            return None
        except:
            print(f"Error in get_future_sell1 (as PERP) for {symbol}: {e}")
            print(format_exc())
            return None

    # get_future_kline - This would map to public_client.get_klines with the PERP symbol if "future" means PERP.
    # The period/granularity mapping would be important. SDK uses KlineIntervalEnum.

    def get_future_account(self, symbol, margin_type='usdt'):
        # The SDK's account_client.get_balances() returns all balances.
        # It does not have a per-symbol or per-margin-type balance query in that way.
        # This method's structure (coin_account endpoint) is not in the SDK.
        # For PERPs, USDC is the main collateral.
        print("get_future_account: SDK does not have a direct 'coin_account' concept for futures. Use general balances.")
        return self.get_spot_account() # Or specifically USDC balance if that's the intent for perp margin


    def get_future_position(self, symbol, margin_type='usdt'):
        # If "future" means PERP, this maps to account_client.get_positions()
        if not self.account_client:
            print("Account client not initialized.")
            return None
        try:
            market_symbol = f"{symbol.upper()}_USDC_PERP"
            positions: List[SDKPosition] = self.account_client.get_positions(symbol=market_symbol)
            # The original return format is a list of positions with many fields.
            # SDKPosition model has fields like symbol, side, quantity, entryPrice etc.
            # This needs careful mapping if the original complex dict structure is required.
            # For simplicity, returning the SDK models as dicts:
            return [p.model_dump(exclude_none=True) for p in positions if p.quantity != "0"] if positions else []
        except:
            print(f"Error in get_future_position (as PERP) for {symbol}: {e}")
            print(format_exc())
            return None


    def get_future_open_order(self, symbol, margin_type='usdt'):
        # If "future" means PERP, this maps to account_client.get_open_orders()
        if not self.account_client:
            print("Account client not initialized.")
            return None
        try:
            market_symbol = f"{symbol.upper()}_USDC_PERP"
            open_orders: List[SDKOrder] = self.account_client.get_open_orders(symbol=market_symbol)
            # Original format is quite specific. Mapping SDKOrder to it:
            formatted_orders = []
            if open_orders:
                for order in open_orders:
                    # This mapping is illustrative, details from original format needed for exact match.
                    formatted_orders.append({
                        'instrument_id': order.symbol,
                        'size': order.quantity,
                        'timestamp': order.createdAt, # SDK createdAt is int timestamp
                        'filled_qty': order.executedQuantity or "0",
                        'fee': '0', # Not directly in SDKOrder
                        'order_id': order.id,
                        'price': order.price or "0",
                        'price_avg': '0', # Not directly in SDKOrder
                        'status': order.status.value, # Enum to string
                        'state': order.status.value, # Mapping 'status' to 'state'
                        'type': f"{order.side.value}_{order.orderType.value}", # e.g. BID_LIMIT - needs mapping to 1,2,3,4
                        # 'contract_val': '10', # Not in SDKOrder
                        # 'leverage': '2', # Not in SDKOrder
                        # 'client_oid': order.clientId,
                        # 'pnl': '0', # Not in SDKOrder
                        # 'order_type': '0' # map from order.orderType
                    })
            return formatted_orders # Or []
        except:
            print(f"Error in get_future_open_order (as PERP) for {symbol}: {e}")
            print(format_exc())
            return None

    def get_future_open_order_id(self, symbol, margin_type):
        open_orders = self.get_future_open_order(symbol, margin_type)
        if open_orders and len(open_orders) > 0:
            return open_orders[0].get('order_id')
        return None

    def get_future_order_info(self, order_id, instrument_id=''):
        # If instrument_id is a PERP symbol
        if not self.account_client:
            print("Account client not initialized.")
            return None
        try:
            # SDK's get_order takes symbol and order_id.
            # Or get_order_history_query for historical.
            # Assuming instrument_id here is the market symbol.
            order_data: SDKOrder = self.account_client.get_order(symbol=instrument_id, order_id=order_id)
            if order_data:
                # Map SDKOrder to the expected dict format. This is illustrative.
                return {
                    'instrument_id': order_data.symbol,
                    'size': order_data.quantity,
                    'timestamp': order_data.createdAt,
                    'filled_qty': order_data.executedQuantity or "0",
                    'fee': '0',
                    'order_id': order_data.id,
                    'price': order_data.price or "0",
                    'price_avg': '0',
                    'status': order_data.status.value,
                    'state': order_data.status.value, # map
                    # ... other fields from original format
                }
            return None
        except:
            print(f"Error in get_future_order_info for {instrument_id}, order {order_id}: {e}")
            print(format_exc())
            return None

    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        # This relies on specific fields from get_future_position's old complex return.
        # If get_future_position now returns list of SDKPosition dicts:
        positions = self.get_future_position(symbol, margin_type)
        if positions and isinstance(positions, list) and len(positions) > 0:
            # Assuming one main position for the symbol. SDKPosition has 'quantity' and 'side'.
            pos_data = positions[0] # This is a dict from model_dump()
            # SDKPosition.quantity is net quantity, side indicates long/short.
            # 'long_avail_qty' / 'short_avail_qty' logic needs to be derived.
            # For simplicity, returning the net quantity if that's useful.
            # Backpack positions are net. "quantity" is signed or use "side".
            # This requires more detailed mapping based on SDKPosition fields.
            # sdk_pos.quantity: "Quantity of the position. Positive for long, negative for short."
            # sdk_pos.side: "Side of the position (e.g. bid, ask)" -> this might be less useful than signed quantity.
            # The old code checks `long_avail_qty` and `short_avail_qty`. The SDK model does not have these.
            # It has `quantity` which is the net exposure.
            # This method's original intent might be hard to map 1:1.
            qty_str = pos_data.get('quantity', "0")
            if float(qty_str) > 0: # Long
                return float(qty_str) # This is net long quantity
            elif float(qty_str) < 0: # Short
                return abs(float(qty_str)) # This is net short quantity (absolute)
        return 0.0


    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        # If "future" means PERP
        if not self.account_client:
            print("Account client not initialized.")
            return None
        try:
            market_symbol = f"{symbol.upper()}_USDC_PERP"
            
            # Map direction: 'buy', 'sell', 'close_buy', 'close_sell' to SDK Side + reduceOnly
            # SDK: side (BID/ASK), orderType, quantity, price. Has reduceOnly in execute_order_advanced.
            # Normal execute_order does not show reduceOnly. Let's check SDK source for execute_order.
            # SDK `execute_order` does not have `reduceOnly`.
            # `bpx.services.trading.execute_order_advanced` does, but that's a lower level call.
            # Let's assume standard open orders for now. "close_buy" (sell to close long) and "close_sell" (buy to close short)
            # might need to be handled by setting reduce_only=True if the SDK supports it easily or using execute_order_advanced.
            # For now, mapping 'buy'/'sell' and ignoring 'close_' prefix for simplicity or assuming non-reduceOnly.
            
            sdk_side = Side.BID if direction.lower() == 'buy' else Side.ASK
            sdk_order_type = OrderType.LIMIT if order_type.lower() == 'limit' else OrderType.MARKET
            
            # The old API had type_dict: {'buy': '1', 'sell': '2', 'close_buy': '3', 'close_sell': '4'}
            # This implies reduceOnly logic. The current basic SDK execute_order might not directly support this distinction.
            # Need to check if `execute_order` supports `reduce_only` or if a different method is needed.
            # The SDK `execute_order` parameters: symbol, side, order_type, quantity, price, time_in_force, client_order_id, post_only, self_trade_prevention, trigger_price, quote_quantity. No `reduceOnly`.
            # This means simple buy/sell. For close_buy/close_sell, this might not work as intended if reduceOnly is critical.

            order_info: SDKOrder = self.account_client.execute_order(
                symbol=market_symbol,
                side=sdk_side,
                order_type=sdk_order_type,
                quantity=str(amount),
                price=str(price)
                # reduceOnly is not a param here.
            )
            # The original return was the raw response from the exchange.
            # SDK returns SDKOrder. We can return its dict representation.
            return order_info.model_dump(exclude_none=True) if order_info else None
        except:
            print(f"Error in place_future_order (as PERP) for {symbol}: {e}")
            print(format_exc())
            return None

    def cancel_future_order(self, order_id, instrument_id):
        # If instrument_id is a PERP symbol
        if not self.account_client:
            print("Account client not initialized.")
            return None
        try:
            cancelled_order: SDKOrder = self.account_client.cancel_order(symbol=instrument_id, order_id=order_id)
            return cancelled_order.model_dump(exclude_none=True) if cancelled_order else None
        except:
            print(f"Error in cancel_future_order (as PERP) for {instrument_id}, order {order_id}: {e}")
            print(format_exc())
            return None

    # endregion

    # region =======================================usdt swap (This section seems to be for PERPETUALS)
    # 获取最新价格
    def get_swap_latest_price(self, symbol: str) -> Optional[float]:
        try:
            return float(self.public_client.get_ticker(symbol=symbol.upper() + '_USDC_PERP')['lastPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.public_client.get_depth(symbol=symbol.upper() + '_USDC_PERP')['bids'][-1][0])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.public_client.get_depth(symbol=symbol.upper() + '_USDC_PERP')['asks'][0][0])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol: str) -> Optional[Dict[str, List[str]]]:
        try:
            info = self.public_client.get_depth(symbol=symbol.upper() + '_USDC_PERP')
            return {
                'bid': [info['bids'][-1][0], info['bids'][-1][1]], 
                'ask': [info['asks'][0][0], info['asks'][0][1]]
            }
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_swap_orderbook(self, symbol: str, limit: int = 5) -> Optional[Dict[str, Any]]:
        try:
            info = self.public_client.get_depth(symbol=symbol.upper() + '_USDC_PERP')
            return {
                'symbol': symbol,
                'bids': info['bids'][-limit:][::-1],
                'asks': info['asks'][:limit]
            }
        except:
            print(format_exc())
            return None

    # TODO：获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol: str) -> Optional[float]:
        pass

    # TODO：获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol: str, limit: int = 5) -> Optional[Dict[str, Any]]:
        pass

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol: str) -> Optional[float]:
        try:
            _price = self.get_swap_latest_price(symbol)
            return float(self.public_client.get_open_interest(symbol=symbol.upper() + '_USDC_PERP')[0]['openInterest']) * _price
        except:
            print(format_exc())
            return None

    # TODO:获取永续合约K线
    def get_swap_kline(self, symbol: str, period: str = '15m', start_time: Optional[int] = None, end_time: Optional[int] = None, limit: Optional[int] = None) -> Optional[List[Dict[str, Any]]]:
        pass

    # 获取永续合约信息(TODO：暂未统一格式)
    def get_swap_instruments_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        # 获取永续合约信息
        symbol: str = 币种
        return: dict = 永续合约信息
        {
            baseSymbol: "SOL",
            createdAt: "2025-01-21T06:34:54.691858",
            filters: {
                price: {
                borrowEntryFeeMaxMultiplier: None,
                borrowEntryFeeMinMultiplier: None,
                maxImpactMultiplier: "1.03",
                maxMultiplier: "1.25",
                maxPrice: None,
                meanMarkPriceBand: { maxMultiplier: "1.15", minMultiplier: "0.9" },
                meanPremiumBand: None,
                minImpactMultiplier: "0.97",
                minMultiplier: "0.75",
                minPrice: "0.01",
                tickSize: "0.01"
                },
                quantity: { maxQuantity: None, minQuantity: "0.01", stepSize: "0.01" }
            },
            fundingInterval: 28800000,
            fundingRateLowerBound: None,
            fundingRateUpperBound: None,
            imfFunction: None,
            marketType: "SPOT",
            mmfFunction: None,
            openInterestLimit: "0",
            orderBookState: "Open",
            quoteSymbol: "USDC",
            symbol: "SOL_USDC"
            }
        """
        try:
            info = self.public_client.get_market()
            for item in info:
                if item['baseSymbol'] == symbol.upper():
                    return item
            return None
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol: str) -> Optional[int]:
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters']['price']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol: str) -> Optional[int]:
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters']['quantity']['stepSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol: str) -> Optional[float]:
        try:
            return float(self.get_swap_instruments_info(symbol)['filters']['quantity']['minQuantity'])
        except:
            print(format_exc())
            return None
        
    # 获取永续合约币对(暂时只有usdc币对)
    def get_swap_instruments_symbols(self, base_symbol: str = 'USDC') -> Optional[List[str]]:
        try:
            symbol_list = []
            info = self.public_client.get_markets()
            for item in info:
                if item['marketType'] == 'PERP':
                    symbol_list.append(item['baseSymbol'].lower())
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdc)
    def get_swap_account_single_asset(self, symbol: str = 'USDC') -> Optional[float]:
        try:
            info = self.account_client.get_balances()
            for k,v in info.items():
                if k == symbol.upper():
                    return float(v['available'])
            return 0.0
        except:
            print(format_exc())
            return None

    # TODO：获取swap账户保证金率(暂时没有理清计算公式)
    def get_swap_margin_rate(self) -> Optional[float]:
        """
        symbol: str = 币种
        return: float = 保证金率
            {
                assetsValue: "3139.***************",
                borrowLiability: "0",
                collateral: [
                    {
                    assetMarkPrice: "166.********",
                    availableQuantity: "0",
                    balanceNotional: "3415.**************",
                    collateralValue: "3074.***************",
                    collateralWeight: "0.9",
                    lendQuantity: "20.510999",
                    openOrderQuantity: "0",
                    symbol: "SOL",
                    totalQuantity: "20.510999"
                    },
                    {
                    assetMarkPrice: "1",
                    availableQuantity: "0",
                    balanceNotional: "64.8304994",
                    collateralValue: "64.8304994",
                    collateralWeight: "1",
                    lendQuantity: "64.8304994",
                    openOrderQuantity: "0",
                    symbol: "USDC",
                    totalQuantity: "64.8304994"
                    }
                ],
                imf: "0.1",
                liabilitiesValue: "0",
                marginFraction: "3.5851716732055815055771273262",
                mmf: "0.0125",
                netEquity: "3139.***************",
                netEquityAvailable: "3052.***************",
                netEquityLocked: "87.*************",
                netExposureFutures: "875.************",
                pnlUnrealized: "0.564139",
                unsettledEquity: "0"
    }
        """
        try:
            info = self.account_client.get_collateral()
            print(info)
            exit()
        except:
            print(f"Error in get_swap_margin_rate: {e}")
            print(format_exc())
            return None
        
    # 获取swap账户持仓信息
    def get_swap_position(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        symbol: str = 币种
        return: list = 持仓信息
            [
                {
                    breakEvenPrice: "104330.14904761904761904761905",
                    cumulativeFundingPayment: "-0.720081",
                    cumulativeInterest: "0",
                    entryPrice: "104193.14166666666666666666667",
                    estLiquidationPrice: "0",
                    imf: "0.02",
                    imfFunction: { base: "0.02", factor: "0.000085", type: "sqrt" },
                    markPrice: "104883.********",
                    mmf: "0.0125",
                    mmfFunction: { base: "0.0125", factor: "0.000051", type: "sqrt" },
                    netCost: "875.22239",
                    netExposureNotional: "881.************",
                    netExposureQuantity: "0.0084",
                    netQuantity: "0.0084",
                    pnlRealized: "9.937806",
                    pnlUnrealized: "-4.136621",
                    positionId: "**********",
                    subaccountId: None,
                    symbol: "BTC_USDC_PERP",
                    userId: 53661
                }
            ]
        """
        try:
            if symbol:
                positions = self.account_client.get_open_positions()
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(i['netQuantity']) > 0 else 'sell',
                        'amount': abs(float(i['netQuantity'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['estLiquidationPrice'],
                        'unrealized_pnl': float(i['pnlUnrealized']) + float(i['pnlRealized']),    # 其他交易所Adapter中没有这个字段
                    } for i in positions if i['symbol'] == symbol.upper() + '_USDC_PERP'
                ]
            else:
                positions = self.account_client.get_open_positions()
                return [
                    {
                        'symbol': i['symbol'].replace('_USDC_PERP', '').lower(),
                        'direction': 'buy' if float(i['netQuantity']) > 0 else 'sell',
                        'amount': abs(float(i['netQuantity'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['estLiquidationPrice'],
                        'unrealized_pnl': float(i['pnlUnrealized']) + float(i['pnlRealized']),    # 其他交易所Adapter中没有这个字段
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        symbol: str = 币种
        return: list = 挂单信息
            [
                {
                    clientId: None,
                    createdAt: 1747622933503,
                    executedQuantity: "0",
                    executedQuoteQuantity: "0",
                    id: "114532216570052608",
                    orderType: "Limit",
                    postOnly: False,
                    price: "165",
                    quantity: "0.01",
                    reduceOnly: False,
                    relatedOrderId: None,
                    selfTradePrevention: "RejectTaker",
                    side: "Bid",
                    status: "New",
                    stopLossLimitPrice: None,
                    stopLossTriggerBy: None,
                    stopLossTriggerPrice: None,
                    symbol: "SOL_USDC_PERP",
                    takeProfitLimitPrice: None,
                    takeProfitTriggerBy: None,
                    takeProfitTriggerPrice: None,
                    timeInForce: "GTC",
                    triggerBy: None,
                    triggerPrice: None,
                    triggerQuantity: None,
                    triggeredAt: None
                }
            ]
        """
        try:
            if symbol:
                open_orders = self.account_client.get_open_orders(symbol=symbol.upper() + '_USDC_PERP')
                return [
                    {
                        'order_id': i['id'],
                        'symbol': symbol,
                        'direction': 'buy' if i['side'] == 'Bid' else 'sell',
                        'order_type': i['orderType'].lower(),
                        'amount': i['quantity'],
                        'price': i['price'],
                        'average_price': None,
                        'remain_amount': float(i['quantity']) - float(i['executedQuantity']),
                    } for i in open_orders
                ]
            else:
                open_orders = self.account_client.get_open_orders()
                return [
                    {
                        'order_id': i['id'],
                        'symbol': i['symbol'].replace('_USDC_PERP', '').lower(),
                        'direction': 'buy' if i['side'] == 'Bid' else 'sell',
                        'order_type': i['orderType'].lower(),
                        'amount': i['quantity'],
                        'price': i['price'],
                        'average_price': None,
                        'remain_amount': float(i['quantity']) - float(i['executedQuantity']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol: str) -> Optional[str]:
        try:
            return self.get_swap_open_order(symbol=symbol)[0]['order_id']
        except:  
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol: str, direction: str, amount: Union[float, str], 
                         price: Optional[Union[float, str]] = None, order_type: str = 'limit', 
                         close_position: Optional[bool] = False) -> Optional[Dict[str, Any]]:
        """
        symbol: str = 币种
        direction: str = 方向
        amount: float = 数量
        price: float = 价格
        order_type: str = 订单类型
        close_position: bool = 是否平仓(暂时无用)
        return: dict = 订单信息
            {
                clientId: None,
                createdAt: 1747624356810,
                executedQuantity: "0.01",
                executedQuoteQuantity: "1.6604",
                id: "114532309847900160",
                orderType: "Market",
                quantity: "0.01",
                quoteQuantity: "1.67",
                reduceOnly: None,
                relatedOrderId: None,
                selfTradePrevention: "RejectTaker",
                side: "Bid",
                status: "Filled",
                stopLossLimitPrice: None,
                stopLossTriggerBy: None,
                stopLossTriggerPrice: None,
                symbol: "SOL_USDC_PERP",
                takeProfitLimitPrice: None,
                takeProfitTriggerBy: None,
                takeProfitTriggerPrice: None,
                timeInForce: "GTC",
                triggerBy: None,
                triggerPrice: None,
                triggerQuantity: None,
                triggeredAt: None
            }
        """
        try:
            if close_position:
                reduce_only = True
            else:
                reduce_only = False
            if order_type == 'limit':
                order_info = self.account_client.execute_order(
                    symbol=symbol.upper() + '_USDC_PERP', 
                    order_type='Limit' if order_type == 'limit' else 'Market',
                    side='Bid' if direction == 'buy' else 'Ask',
                    quantity=str(amount),
                    price=price,
                    reduce_only=reduce_only
                )
            else:
                order_info = self.account_client.execute_order(
                    symbol=symbol.upper() + '_USDC_PERP', 
                    order_type='Market',
                    side='Bid' if direction == 'buy' else 'Ask',
                    quantity=str(amount),
                    reduce_only=reduce_only,
                )
            if 'Invalid' not in str(order_info):
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['id'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount if order_type == 'limit' else order_info['quantity'],
                    'price': price if order_type == 'limit' else '',
                    'order_type': order_type,
                }
            else:
                print(order_info)
                return None
        except:
            print(format_exc())
            return None

    # 查询订单
    def get_swap_order_info(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]:
        """
        symbol: str = 币种
        order_id: str = 订单号
        return: dict = 订单信息
        [
            {
                clientId: None,
                createdAt: "2025-05-19T02:48:53.503",
                executedQuantity: "0",
                executedQuoteQuantity: "0",
                expiryReason: None,
                id: "114532216570052608",
                orderType: "Limit",
                postOnly: False,
                price: "165",
                quantity: "0.01",
                quoteQuantity: "1.65",
                selfTradePrevention: "RejectTaker",
                side: "Bid",
                status: "New",
                stopLossLimitPrice: None,
                stopLossTriggerBy: None,
                stopLossTriggerPrice: None,
                symbol: "SOL_USDC_PERP",
                takeProfitLimitPrice: None,
                takeProfitTriggerBy: None,
                takeProfitTriggerPrice: None,
                timeInForce: "GTC",
                triggerBy: None,
                triggerPrice: None,
                triggerQuantity: None
            }
        ]
        """
        try:
            order_info = self.account_client.get_order_history(symbol=symbol.upper() + '_USDC_PERP', order_id=order_id)
            if order_info:
                info = order_info[0]
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_id,
                    'symbol': symbol,
                    'direction': 'buy' if info['side'] == 'Bid' else 'sell',
                    'order_type': info['orderType'].lower(),
                    'amount': info['quantity'],
                    'price': info['price'] if info['status'] == 'New' else '',      # 下单价
                    'average_price': info['price'] if info['status'] == 'Filled' else '',     # 成交价
                    'remain_amount': float(info['quantity']) - float(info['executedQuantity']),
                    'fee': None,
                }
            return None
        except:
            print(f"Error in get_swap_order_info for {symbol}, order {order_id}: {e}")
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]:
        """
        symbol: str = 币种
        order_id: str = 订单号
        return: dict = 取消订单信息
            {
                clientId: None,
                createdAt: 1747622933503,
                executedQuantity: "0",
                executedQuoteQuantity: "0",
                id: "114532216570052608",
                orderType: "Limit",
                postOnly: False,
                price: "165",
                quantity: "0.01",
                reduceOnly: False,
                relatedOrderId: None,
                selfTradePrevention: "RejectTaker",
                side: "Bid",
                status: "Cancelled",
                stopLossLimitPrice: None,
                stopLossTriggerBy: None,
                stopLossTriggerPrice: None,
                symbol: "SOL_USDC_PERP",
                takeProfitLimitPrice: None,
                takeProfitTriggerBy: None,
                takeProfitTriggerPrice: None,
                timeInForce: "GTC",
                triggerBy: None,
                triggerPrice: None,
                triggerQuantity: None,
                triggeredAt: None
                }
        """
        try:
            cancel_order_info = self.account_client.cancel_order(symbol=symbol.upper() + '_USDC_PERP', order_id=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['status'] == 'Cancelled' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # 获取下单记录（注意：获取到的是成交明细）
    def fetch_my_swap_orders(self, symbol, date=1, direction='buy'):
        """
        :param symbol:
        :param date:
        :param direction:
        :return:
            [
                {
                    clientId: None,
                    fee: "0.138875",
                    feeSymbol: "USDC",
                    isMaker: True,
                    orderId: "**********",
                    price: "3.6059",
                    quantity: "202.7",
                    side: "Bid",
                    symbol: "IP_USDC_PERP",
                    systemOrderType: None,
                    timestamp: "2025-06-16T10:18:30.116",
                    tradeId: 446881
                },
                {
                    clientId: None,
                    fee: "0.138836",
                    feeSymbol: "USDC",
                    isMaker: True,
                    orderId: "**********",
                    price: "3.6049",
                    quantity: "202.7",
                    side: "Bid",
                    symbol: "IP_USDC_PERP",
                    systemOrderType: None,
                    timestamp: "2025-06-16T10:17:53.048",
                    tradeId: 446880
                },
                ......
            ]
        """
        days_ago = int(time() * 1000) - date * 24 * 60 * 60 * 1000
        info = self.account_client.get_fill_history(symbol=f"{symbol.upper()}_USDC_PERP", limit=1000)
        info.reverse()
        df = pd.DataFrame(info)
        if pd.to_datetime(info[0]['timestamp']).timestamp() > days_ago:
            while True:
                end_time = pd.to_datetime(info[0]['timestamp']).timestamp() * 1000

                info = self.account_client.get_fill_history(symbol=f"{symbol.upper()}USDC_PERP", limit=1000, endTime=end_time)
                if pd.to_datetime(info[0]['timestamp']).timestamp() == pd.to_datetime(df.iloc[-1]['timestamp']).timestamp():
                    break
                df = pd.concat([df, pd.DataFrame(info)], axis=0)
                if pd.to_datetime(info[0]['timestamp']).timestamp() < days_ago:
                    break
        df['datetime'] = pd.to_datetime(df['timestamp']) + timedelta(hours=8)
        df = df[df['side']=='Bid' if direction.upper() == 'BUY' else 'Ask']
        df.sort_values(by='datetime', ascending=False, inplace=True)
        df.drop_duplicates(subset=['tradeId'], keep='first', inplace=True)
        # 截取最近两天时间的数据
        df = df[df['datetime'] > pd.to_datetime(days_ago, unit='ms')]
        df.reset_index(drop=True, inplace=True)
        df['quantity'] = df['quantity'].astype(float)
        df['price'] = df['price'].astype(float)
        df['amount'] = (df['quantity'] * df['price']).astype(float)
        df['fee'] = df['fee'].astype(float)
        return df
    
    # endregion

    # TODO：币本位合约
    # region =======================================coin swap
    
    # endregion


# a = BackpackAdapter()
# # # b = a.cancel_swap_order('sol', '114532216570052608')
# print(a.cancel_spot_order('sol', '114527497563734016'))
# # # print(a.get_swap_open_order('sol'))
# # b = a.place_swap_order('sol', 'buy', 0.01, order_type='market')
# b = a.fetch_my_swap_orders('ip', date=7)
# 获取quantity列的和
# print(b['quantity'].sum())
# print(b)