"""
备注说明：
    官方sdk:
    https://github.com/bybit-exchange/pybit
    pip install pybit
    
    
"""
# region ================================================import
from math import ceil, log
from traceback import format_exc
import sys
from os.path import abspath, dirname
sys.path.insert(0, dirname(dirname(abspath(__file__))))
from .ExchangeAdapter import ExchangeAdapter
from .api_config import bybit_api_config
from pybit.unified_trading import HTTP
# endregion ================================================import


class BybitAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'):
        self._api_config = bybit_api_config.api_config
        self.account = account
        self.exchange_name = 'bybit'
        # 创建交易所
        self.exchange = HTTP()
        if sys.platform == 'linux':
            self.exchange.api_key = self._api_config[account]['api_key']
            self.exchange.api_secret = self._api_config[account]['secret_key']
        else:
            self.exchange.api_key = self._api_config['查询']['api_key']
            self.exchange.api_secret = self._api_config['查询']['secret_key']
        # self.uuid = self.exchange.uuid()

    # region ==============================================spot
    # 获取spot最新成交价格

    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return (self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='spot')['result']['list'][0]['lastPrice'])

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:

        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='spot')['result']['list'][0]['bid1Price'])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:

        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='spot')['result']['list'][0]['ask1Price'])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        {
            "symbol": "ETHUSDT",
            "bid1Price": "2526.37",
            "bid1Size": "5.63018",
            "ask1Price": "2526.38",
            "ask1Size": "7.73519",
            "lastPrice": "2526.39",
            "prevPrice24h": "2516.73",
            "price24hPcnt": "0.0038",
            "highPrice24h": "2552.98",
            "lowPrice24h": "2430.82",
            "turnover24h": "397693954.1010337",
            "volume24h": "159037.92775",
            "usdIndexPrice": "2526.720573"
            }
        """
        try:
            raw_orderbook = self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='spot')['result']['list'][0]
            return {
                'bid': [raw_orderbook['bid1Price'], raw_orderbook['bid1Size']],
                'ask': [raw_orderbook['ask1Price'], raw_orderbook['ask1Size']]
            }
        except:
            print(format_exc())
            return None

    # 获取spot orderbook
    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:

        """
        try:
            raw_orderbook = self.exchange.get_orderbook(symbol=symbol.upper() + 'USDT', limit=limit, category='spot')['result']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['b'],
                'asks': raw_orderbook['a']
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self):
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            symbol: "ZKUSDT",
            baseCoin: "ZK",
            quoteCoin: "USDT",
            innovation: "0",
            status: "Trading",
            marginTrading: "utaOnly",
            lotSizeFilter: {
                basePrecision: "0.01",
                quotePrecision: "0.000001",
                minOrderQty: "10",
                maxOrderQty: "8000000",
                minOrderAmt: "1",
                maxOrderAmt: "200000"
            },
            priceFilter: { tickSize: "0.0001" },
            riskParameters: { limitParameter: "0.05", marketParameter: "0.05" }
            }
        """
        try:
            return self.exchange.get_instruments_info(category='spot', symbol=symbol.upper() + 'USDT')['result']['list'][0]
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(category='spot', symbol=symbol.upper() + 'USDT')['result']['list'][0]['priceFilter']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(category='spot', symbol=symbol.upper() + 'USDT')['result']['list'][0]['lotSizeFilter']['basePrecision']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(category='spot', symbol=(symbol + base_symbol).upper())['result']['list'][0]['priceFilter']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(category='spot', symbol=(symbol + base_symbol).upper())['result']['list'][0]['lotSizeFilter']['basePrecision']), 10), 0))
        except:
            print(format_exc())
            return None

    # FIXME:bybit获取的最小下单数量有问题,实际以minOrderQty数量为准，minOrderAmt无效
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.get_instruments_info(category='spot', symbol=symbol.upper() + 'USDT')['result']['list'][0]['lotSizeFilter']['minOrderQty'])
            # _min_amount = float(_info['lotSizeFilter']['minOrderQty'])
            # _min_nominal = float(_info['lotSizeFilter']['minOrderAmt'])
            # _price = self.get_spot_last_price(symbol)
            # _amount_precision = max(int(round(log(1 / float(_info['lotSizeFilter']['basePrecision']), 10), 0)), 0)
            # _min_amount2 = ceil(_min_nominal * 1.05 / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            # return max(_min_amount, _min_amount2)
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
        :return:
        {
            "symbol": "BTCUSDT",
            "baseCoin": "BTC",
            "quoteCoin": "USDT",
            "innovation": "0",
            "status": "Trading",
            "marginTrading": "utaOnly",
            "lotSizeFilter": {
                "basePrecision": "0.000001",
                "quotePrecision": "0.00000001",
                "minOrderQty": "0.000048",
                "maxOrderQty": "71.73956243",
                "minOrderAmt": "1",
                "maxOrderAmt": "4000000"
            },
            "priceFilter": {
                "tickSize": "0.01"
            },
            "riskParameters": {
                "limitParameter": "0.03",
                "marketParameter": "0.03"
            }
            }
        """
        try:
            symbol_list = []
            _info = self.exchange.get_instruments_info(category='spot')['result']['list']
            symbol_list.extend([i['symbol'].lower()
                               for i in _info if i['status'] == 'Trading'])
            base_symbol_len = len(base_symbol)
            symbol_list = [
                i for i in symbol_list if i[-base_symbol_len:] == base_symbol]
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        返回的是扣除持仓亏损后的余额
        {
            coin: "USDT",
            transferBalance: "2297.2658",
            walletBalance: "48196.1829",
            bonus: ""
            }
        """
        try:
            return float(self.exchange.get_coin_balance(account_type='UNIFIED', coin=symbol.upper())['result']['balance']['transferBalance'])
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
            {
                totalEquity: "327182.********",
                accountIMRate: "0.0865",
                totalMarginBalance: "320652.********",
                totalInitialMargin: "27757.********",
                accountType: "UNIFIED",
                totalAvailableBalance: "292894.********",
                accountMMRate: "0.0721",
                totalPerpUPL: "-47517.********",
                totalWalletBalance: "368169.********",
                accountLTV: "0",
                totalMaintenanceMargin: "23131.********",
                coin: [
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "2.99997",
                    totalPositionMM: "",
                    usdValue: "326481.********",
                    unrealisedPnl: "0",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "2.99997",
                    cumRealisedPnl: "0",
                    locked: "0",
                    marginCollateral: True,
                    coin: "BTC"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "0",
                    totalPositionMM: "",
                    usdValue: "0.00002037",
                    unrealisedPnl: "0",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "0",
                    cumRealisedPnl: "-0.********",
                    locked: "0",
                    marginCollateral: True,
                    coin: "ETH"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "700.442216",
                    totalPositionMM: "",
                    usdValue: "700.76091721",
                    unrealisedPnl: "-47495.7407",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "48196.182916",
                    cumRealisedPnl: "455561.48687377",
                    locked: "0",
                    marginCollateral: True,
                    coin: "USDT"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "0.40661902",
                    totalPositionMM: "",
                    usdValue: "0.29413681",
                    unrealisedPnl: "0",
                    collateralSwitch: False,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "0.40661902",
                    cumRealisedPnl: "-0.091425",
                    locked: "0",
                    marginCollateral: True,
                    coin: "MNT"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "0.00000036",
                    totalPositionMM: "",
                    usdValue: "0.00000036",
                    unrealisedPnl: "0",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "0.00000036",
                    cumRealisedPnl: "-21485.60013287",
                    locked: "0",
                    marginCollateral: True,
                    coin: "USDC"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "",
                    availableToWithdraw: "",
                    totalOrderIM: "",
                    equity: "0.0093",
                    totalPositionMM: "",
                    usdValue: "0.00147937",
                    unrealisedPnl: "0",
                    collateralSwitch: False,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "",
                    walletBalance: "0.0093",
                    cumRealisedPnl: "0",
                    locked: "0",
                    marginCollateral: False,
                    coin: "OBOL"
                    }
                ]
                }
        """
        try:
            balance = self.exchange.get_wallet_balance(accountType='UNIFIED')['result']['list'][0]['coin']
            return {i['coin'].lower(): i['equity'] for i in balance}
        except:
            print(format_exc())
            return None

    # 获取spot账户挂单信息
    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
            [
                {
                    "symbol": "ETHUSDT",
                    "orderType": "Limit",
                    "orderLinkId": "*************",
                    "slLimitPrice": "0",
                    "orderId": "1764562230223045376",
                    "cancelType": "UNKNOWN",
                    "avgPrice": "0.00",
                    "stopOrderType": "",
                    "lastPriceOnCreated": "",
                    "orderStatus": "New",
                    "takeProfit": "0",
                    "cumExecValue": "0.0000000",
                    "smpType": "None",
                    "triggerDirection": "0",
                    "blockTradeId": "",
                    "isLeverage": "0",
                    "rejectReason": "EC_NoError",
                    "price": "2500.00",
                    "orderIv": "",
                    "createdTime": "1725088209834",
                    "tpTriggerBy": "",
                    "positionIdx": "0",
                    "trailingPercentage": "0",
                    "timeInForce": "GTC",
                    "leavesValue": "25.0000000",
                    "basePrice": "2522.88",
                    "updatedTime": "1725088209835",
                    "side": "Buy",
                    "smpGroup": "0",
                    "triggerPrice": "0.00",
                    "tpLimitPrice": "0",
                    "trailingValue": "0",
                    "cumExecFee": "0",
                    "leavesQty": "0.01000",
                    "slTriggerBy": "",
                    "closeOnTrigger": false,
                    "placeType": "",
                    "cumExecQty": "0.00000",
                    "reduceOnly": false,
                    "activationPrice": "0",
                    "qty": "0.01000",
                    "stopLoss": "0",
                    "marketUnit": "",
                    "smpOrderId": "",
                    "triggerBy": ""
                }
            ]
        """
        try:
            open_orders = self.exchange.get_open_orders(symbol=symbol.upper() + 'USDT', category='spot')['result']['list']
            return [{
                'order_id': order['orderId'],
                'symbol': symbol,
                'direction': order['side'].lower(),
                'amount': order['qty'],
                'price': order['price'],
                'order_type': order['orderType'].lower(),
                'average_price': order['avgPrice'],
                'remain_amount': order['leavesQty'],
            } for order in open_orders]
        except:
            print(format_exc())
            return None

    # 下单
    def place_spot_order(self, symbol, direction, amount=None, price=None, order_type='limit', quoteorderqty=None):
        """
        :param symbol:
        :param direction:
        :param amount:  limit下单时，amount表示下单数量；market下单，amount表示下单金额
        :param price:
        :param order_type:
        :param params:
        :return:
        {'orderId': '1960497459490980609', 'orderLinkId': '1960497459490980610'}
        """
        try:
            if order_type == 'limit':
                order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='spot', side=direction, order_type=order_type, price=str(price), qty=str(amount))['result']
            elif order_type == 'market':
                # 按拟成交金额下单
                if quoteorderqty:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='spot', side=direction, order_type=order_type, qty=str(quoteorderqty), marketUnit='quoteCoin')['result']
                else:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='spot', side=direction, order_type=order_type, qty=str(amount), marketUnit='baseCoin')['result']
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                # 在市价单quoteorderqty情况下，amount表示quoteorderqty，这里amount显示的数据是quoteorderqty(usdt)
                'amount': amount if order_type == 'limit' else quoteorderqty,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 下单2
    def place_spot_order2(self, symbol, base_symbol, direction, amount=None, price=None, order_type='limit', quoteorderqty=None):
        try:
            if order_type == 'limit':
                order_info = self.exchange.place_order(symbol=(symbol+ base_symbol).upper(), category='spot', side=direction, order_type=order_type, price=str(price), qty=str(amount))['result']
            elif order_type == 'market':
                # 按拟成交金额下单,参数amount表示下单金额
                if quoteorderqty:
                    order_info = self.exchange.place_order(symbol=(symbol+ base_symbol).upper(), category='spot', side=direction, order_type=order_type, qty=str(quoteorderqty), marketUnit='quoteCoin')['result']
                else:
                    order_info = self.exchange.place_order(symbol=(symbol+ base_symbol).upper(), category='spot', side=direction, order_type=order_type, qty=str(amount), marketUnit='baseCoin')['result']
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol + '/' + base_symbol,
                'direction': direction,
                # 在市价单quoteorderqty情况下，amount表示quoteorderqty，这里amount显示的数据是quoteorderqty(usdt)
                'amount': amount if order_type == 'limit' else quoteorderqty,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_spot_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
            symbol: "BTCUSDT",
            orderType: "Limit",
            orderLinkId: "1748441868312",
            slLimitPrice: "0",
            orderId: "1960466917550719744",
            cancelType: "CancelByUser",
            avgPrice: "",
            stopOrderType: "",
            lastPriceOnCreated: "",
            orderStatus: "Cancelled",
            takeProfit: "0",
            cumExecValue: "0.0000000",
            smpType: "None",
            triggerDirection: 0,
            blockTradeId: "",
            rejectReason: "EC_PerCancelRequest",
            isLeverage: "0",
            price: "102000.0",
            orderIv: "",
            createdTime: "1748441868429",
            tpTriggerBy: "",
            positionIdx: 0,
            trailingPercentage: "0",
            timeInForce: "GTC",
            leavesValue: "0",
            basePrice: "107781.3",
            updatedTime: "1748441913204",
            side: "Buy",
            smpGroup: 0,
            triggerPrice: "0.0",
            tpLimitPrice: "0",
            trailingValue: "0",
            cumExecFee: "0",
            slTriggerBy: "",
            leavesQty: "0",
            closeOnTrigger: False,
            slippageToleranceType: "UNKNOWN",
            placeType: "",
            cumExecQty: "0",
            reduceOnly: False,
            activationPrice: "0",
            qty: "0.000100",
            stopLoss: "0",
            marketUnit: "",
            smpOrderId: "",
            slippageTolerance: "",
            triggerBy: ""
            }
        """
        try:
            order_info = self.exchange.get_open_orders(symbol=symbol.upper() + 'USDT', category='spot', orderId=order_id)['result']['list'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['orderType'].lower(),
                'amount': order_info['qty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': order_info['leavesQty'],
                'fee': order_info['cumExecFee']     # usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息2
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            order_info = self.exchange.get_open_orders(symbol=(symbol+ base_symbol).upper(), category='spot', orderId=order_id)['result']['list'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['orderType'].lower(),
                'amount': order_info['qty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': order_info['leavesQty'],
                'fee': order_info['cumExecFee']     # usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_spot_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
            retCode: 0,
            retMsg: "OK",
            result: { orderId: "1960466917550719744", orderLinkId: "1748441868312" },
            retExtInfo: {},
            time: 1748441913203
            }
        """
        try:
            order_info = self.exchange.cancel_order(symbol=symbol.upper() + 'USDT', category='spot', orderId=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['retMsg'] == 'OK' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO 交割合约
    # region ==============================================future
    # 获取交割合约信息
    def get_future_instruments_info(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        :param symbol:
        :param margin_type: 币本位coin or U本位usdt
        :param contract_type: this_week, next_week, quarter, next_quarter
        :return:
            {
                'alias': 'this_week',
                'baseCcy': '',
                'category': '1', 手续费档位，每个交易产品属于哪个档位手续费
                'ctMult': '1',  合约乘数，仅适用于交割/永续/期权
                'ctType': 'linear',
                'ctVal': '0.1', 合约面值，仅适用于交割/永续/期权
                'ctValCcy': 'ETH',  合约面值计价币种，仅适用于交割/永续/期权
                'expTime': '1633075200000',
                'instId': 'ETH-USDT-211001',
                'instType': 'FUTURES',
                'lever': '125',
                'listTime': '1631866200880',
                'lotSz': '1',   下单数量精度，如 BTC-USDT-SWAP：1
                'minSz': '1',   最小下单数量
                'optType': '',
                'quoteCcy': '',
                'settleCcy': 'USDT',    盈亏结算和保证金币种，如 BTC 仅适用于交割/永续/期权
                'state': 'live',
                'stk': '',
                'tickSz': '0.01',   下单价格精度，如 0.0001
                'uly': 'ETH-USDT'
            }
        """
        instId = self.get_futures_instrument_id(
            symbol, margin_type=margin_type, contract_type=contract_type)
        info = self.exchange.get_instruments(
            instType='FUTURES', instId=instId)['data'][0]

        return info

    # 获取合约当期instId
    def get_futures_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.exchange.get_instruments(instType='FUTURES')[
            'data']

        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['uly'] == symbol.upper() + '-' + margin_type2 and i['alias'] == contract_type:
                    return i['instId']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['uly'] == symbol.upper() + '-' + margin_type2:
                    instrument_id_list.append(i['instId'])
            return instrument_id_list

    # 获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.get_futures_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['bidPx'])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['bidPx'])

    # 获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.get_futures_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['askPx'])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['askPx'])

    # 获取future-K线
    def get_future_kline(self, symbol, period='15m', start='', end='', limit=100, margin_type='usdt', contract_type='quarter'):
        """

        @param symbol:  btc
        @param period:  时间周期,如 [1m/3m/5m/15m/30m/1H/2H/4H/6H/12H/1D/1W/1M/3M/6M/1Y]
        @param start:  str
        @param end:   str
        @param margin_type:   str
        @param contract_type:   str
        @param limit: float 最大100，默认100
        @return:    spot：# 默认100根K线
        """
        # 获取起始时间
        if start and end:
            start = datetimeConvertToMs((pd.to_datetime(start)))
            end = datetimeConvertToMs(pd.to_datetime(end))
        # =======================================现货K线
        instId = self.get_futures_instrument_id(
            symbol, margin_type=margin_type, contract_type=contract_type)

        kline = self.exchange.get_candlesticks(
            instId=instId, bar=period, before=start, after=end, limit=limit)
        # 转为Dataframe，转为北京时区
        kline = pd.DataFrame(kline['data'], columns=[
                             'candle_begin_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy'])
        kline['candle_begin_time'] = pd.to_datetime(
            kline['candle_begin_time'], unit='ms') + timedelta(hours=8)
        kline.sort_values(by='candle_begin_time', ascending=True, inplace=True)
        kline.reset_index(drop=True, inplace=True)

        return kline

    # # 获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # <editor-fold desc="# ====================== todo future">
    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion


    # region ==============================================usdt swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['lastPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['bid1Price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['ask1Price'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
            {
            "symbol": "ETHUSDT",
            "lastPrice": "2520.59",
            "indexPrice": "2521.87",
            "markPrice": "2520.59",
            "prevPrice24h": "2465.23",
            "price24hPcnt": "0.022456",
            "highPrice24h": "2542.73",
            "lowPrice24h": "2428.56",
            "prevPrice1h": "2520.11",
            "openInterest": "620854.91",
            "openInterestValue": "1564920677.60",
            "turnover24h": "1604957059.1999",
            "volume24h": "644536.7000",
            "fundingRate": "0.00000406",
            "nextFundingTime": "1725120000000",
            "predictedDeliveryPrice": "",
            "basisRate": "",
            "deliveryFeeRate": "",
            "deliveryTime": "0",
            "ask1Size": "64.18",
            "bid1Price": "2520.58",
            "ask1Price": "2520.59",
            "bid1Size": "0.50",
            "basis": "",
            "preOpenPrice": "",
            "preQty": "",
            "curPreListingPhase": ""
            }
        """
        try:
            raw_orderbook = self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]
            return {
                'bid': [raw_orderbook['bid1Price'], raw_orderbook['bid1Size']],
                'ask': [raw_orderbook['ask1Price'], raw_orderbook['ask1Size']]
            }
        except:
            print(format_exc())
            return None

    # 获取swap盘口
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:

        """
        try:
            raw_orderbook = self.exchange.get_orderbook(symbol=symbol.upper() + 'USDT', limit=limit, category='linear')['result']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['b'],
                'asks': raw_orderbook['a']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:

        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['fundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在

        """
        try:
            funding_rates = self.exchange.get_funding_rate_history(symbol=symbol.upper() + 'USDT', category='linear', limit=limit)['result']['list']
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingRateTimestamp'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.get_tickers(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['openInterestValue'])
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
            {
            "symbol": "ETHUSDT",
            "contractType": "LinearPerpetual",
            "status": "Trading",
            "baseCoin": "ETH",
            "quoteCoin": "USDT",
            "launchTime": "1615766400000",
            "deliveryTime": "0",
            "deliveryFeeRate": "",
            "priceScale": "2",
            "leverageFilter": {
                "minLeverage": "1",
                "maxLeverage": "100.00",
                "leverageStep": "0.01"
            },
            "priceFilter": {
                "minPrice": "0.01",
                "maxPrice": "19999.98",
                "tickSize": "0.01"
            },
            "lotSizeFilter": {
                "maxOrderQty": "7240.00",
                "minOrderQty": "0.01",
                "qtyStep": "0.01",
                "postOnlyMaxOrderQty": "7240.00",
                "maxMktOrderQty": "724.00",
                "minNotionalValue": "5"
            },
            "unifiedMarginTrade": true,
            "fundingInterval": "480",
            "settleCoin": "USDT",
            "copyTrading": "both",
            "upperFundingRate": "0.00375",
            "lowerFundingRate": "-0.00375",
            "isPreListing": false,
            "preListingInfo": "None"
            }
        """
        try:
            return self.exchange.get_instruments_info(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['priceFilter']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return max(int(round(log(1 / float(self.exchange.get_instruments_info(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['lotSizeFilter']['qtyStep']), 10), 0)), 0)
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.get_instruments_info(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]
            _min_amount = float(_info['lotSizeFilter']['minOrderQty'])
            _min_nominal = float(_info['lotSizeFilter']['minNotionalValue'])
            _price = self.get_swap_latest_price(symbol)
            _amount_precision = max(int(round(log(1 / float(_info['lotSizeFilter']['qtyStep']), 10), 0)), 0)
            _min_amount2 = ceil(_min_nominal / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            return ceil(max(_min_amount, _min_amount2) / _min_amount) * _min_amount
        except:
            print(format_exc())
            return None

    # 获取永续合约币对
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:  usdt, usdc
        :return:
        {
            "symbol": "10000000AIDOGEUSDT",
            "contractType": "LinearPerpetual",
            "status": "Trading",
            "baseCoin": "10000000AIDOGE",
            "quoteCoin": "USDT",
            "launchTime": "1709542899000",
            "deliveryTime": "0",
            "deliveryFeeRate": "",
            "priceScale": "6",
            "leverageFilter": {
                "minLeverage": "1",
                "maxLeverage": "12.50",
                "leverageStep": "0.01"
            },
            "priceFilter": {
                "minPrice": "0.000001",
                "maxPrice": "1.999998",
                "tickSize": "0.000001"
            },
            "lotSizeFilter": {
                "maxOrderQty": "25000000",
                "minOrderQty": "100",
                "qtyStep": "100",
                "postOnlyMaxOrderQty": "25000000",
                "maxMktOrderQty": "5000000",
                "minNotionalValue": "5"
            },
            "unifiedMarginTrade": true,
            "fundingInterval": "480",
            "settleCoin": "USDT",
            "copyTrading": "none",
            "upperFundingRate": "0.03",
            "lowerFundingRate": "-0.03",
            "isPreListing": false,
            "preListingInfo": "None"
            }
        """
        try:
            symbol_list = []
            _info = self.exchange.get_instruments_info(category='linear')['result']['list']
            if base_symbol.lower() == 'usdt':
                symbol_list.extend(i['symbol'].lower() for i in _info if all([i['status'] == 'Trading', i['quoteCoin'] == 'USDT', i['contractType'] == 'LinearPerpetual']))
            elif base_symbol.lower() == 'usdc':
                symbol_list.extend(i['symbol'].lower() for i in _info if all([i['status'] == 'Trading', i['quoteCoin'] == 'USDC', i['contractType'] == 'LinearPerpetual']))
                # 将元素中的perp替换为usdc
                symbol_list = [i.replace('perp', 'usdc') for i in symbol_list]
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(统一账户，和现货账户一致，单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        """
        :param symbol:
        :return:
            统一账户
        """
        try:
            return float(self.exchange.get_coin_balance(coin=symbol.upper(), account_type='UNIFIED')['result']['balance']['transferBalance'])
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
            {
                totalEquity: "49.********",
                accountIMRate: "0.0295",
                totalMarginBalance: "45.********",
                totalInitialMargin: "1.********",
                accountType: "UNIFIED",
                totalAvailableBalance: "43.********",
                accountMMRate: "0.0021",
                totalPerpUPL: "-0.********",
                totalWalletBalance: "45.2214246",
                accountLTV: "0.0014",
                totalMaintenanceMargin: "0.********",
                coin: [
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "0.********",
                    totalOrderIM: "0",
                    equity: "0.********",
                    totalPositionMM: "0",
                    usdValue: "47.********",
                    unrealisedPnl: "0",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "0",
                    walletBalance: "0.********",
                    cumRealisedPnl: "-0.********",
                    locked: "0",
                    marginCollateral: True,
                    coin: "ETH"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0.00000038",
                    availableToWithdraw: "0",
                    totalOrderIM: "0",
                    equity: "-0.06423901",
                    totalPositionMM: "0.09232001",
                    usdValue: "-0.06424023",
                    unrealisedPnl: "-0.057",
                    collateralSwitch: True,
                    spotHedgingQty: "0",
                    borrowAmount: "0.064239012410000000",
                    totalPositionIM: "1.32633301",
                    walletBalance: "-0.00723901",
                    cumRealisedPnl: "1801.78252903",
                    locked: "0",
                    marginCollateral: True,
                    coin: "USDT"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "",
                    availableToWithdraw: "0.00825556",
                    totalOrderIM: "0",
                    equity: "0.00825556",
                    totalPositionMM: "0",
                    usdValue: "0.00007324",
                    unrealisedPnl: "0",
                    collateralSwitch: False,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "0",
                    walletBalance: "0.00825556",
                    cumRealisedPnl: "0",
                    locked: "0",
                    marginCollateral: False,
                    coin: "BLAST"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "0",
                    availableToWithdraw: "2.76641604",
                    totalOrderIM: "0",
                    equity: "2.76641604",
                    totalPositionMM: "0",
                    usdValue: "1.52654986",
                    unrealisedPnl: "0",
                    collateralSwitch: False,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "0",
                    walletBalance: "2.76641604",
                    cumRealisedPnl: "-0.091425",
                    locked: "0",
                    marginCollateral: True,
                    coin: "MNT"
                    },
                    {
                    availableToBorrow: "",
                    bonus: "0",
                    accruedInterest: "",
                    availableToWithdraw: "0.0085",
                    totalOrderIM: "0",
                    equity: "0.0085",
                    totalPositionMM: "0",
                    usdValue: "0.00147052",
                    unrealisedPnl: "0",
                    collateralSwitch: False,
                    spotHedgingQty: "0",
                    borrowAmount: "0.000000000000000000",
                    totalPositionIM: "0",
                    walletBalance: "0.0085",
                    cumRealisedPnl: "-0.05",
                    locked: "0",
                    marginCollateral: False,
                    coin: "PIRATE"
                    }
                ]
                }
        """
        try:
            return float(self.exchange.get_wallet_balance(accountType='UNIFIED')['result']['list'][0]['accountMMRate'])
        except:
            print(format_exc())
            return None

    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        :param symbol:
        :return:
        [
            {
                "symbol": "ETHUSDT",
                "leverage": "10",
                "autoAddMargin": "0",
                "avgPrice": "2500",
                "liqPrice": "",
                "riskLimitValue": "900000",
                "takeProfit": "",
                "positionValue": "25",
                "isReduceOnly": false,
                "tpslMode": "Full",
                "riskId": "11",
                "trailingStop": "0",
                "unrealisedPnl": "-0.0839",
                "markPrice": "2508.39",
                "adlRankIndicator": "2",
                "cumRealisedPnl": "-0.005",
                "positionMM": "0.140125",
                "createdTime": "*************",
                "positionIdx": "0",
                "positionIM": "2.515125",
                "seq": "165542283879",
                "updatedTime": "1725270630674",
                "side": "Sell",
                "bustPrice": "",
                "positionBalance": "0",
                "leverageSysUpdatedTime": "",
                "curRealisedPnl": "-0.005",
                "size": "0.01",
                "positionStatus": "Normal",
                "mmrSysUpdatedTime": "",
                "stopLoss": "",
                "tradeMode": "0",
                "sessionAvgPrice": ""
            }
            ]
        """
        try:
            if symbol:
                positions = self.exchange.get_positions(symbol=symbol.upper() + 'USDT', category='linear')['result']['list']
                return [
                    {
                        'symbol': symbol,
                        'direction': i['side'].lower(),
                        'amount': i['size'],
                        'price': i['avgPrice'],
                        'liquidation_price': i['liqPrice'],
                    } for i in positions if i['size'] != '0'
                ]
            else:
                positions = self.exchange.get_positions(category='linear', settleCoin='USDT')['result']['list']
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'amount': i['size'],
                        'price': i['avgPrice'],
                        'liquidation_price': i['liqPrice'],
                    } for i in positions if i['size'] != '0'
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        :param symbol:
        :return:
            [
                {
                    "symbol": "ETHUSDT",
                    "orderType": "Limit",
                    "orderLinkId": "",
                    "slLimitPrice": "0",
                    "orderId": "d867908f-217f-4a37-ab3e-86b5e63decbd",
                    "cancelType": "UNKNOWN",
                    "avgPrice": "",
                    "stopOrderType": "",
                    "lastPriceOnCreated": "2433.06",
                    "orderStatus": "New",
                    "createType": "CreateByUser",
                    "takeProfit": "",
                    "cumExecValue": "0",
                    "tpslMode": "",
                    "smpType": "None",
                    "triggerDirection": "0",
                    "blockTradeId": "",
                    "isLeverage": "",
                    "rejectReason": "EC_NoError",
                    "price": "2500",
                    "orderIv": "",
                    "createdTime": "*************",
                    "tpTriggerBy": "",
                    "positionIdx": "0",
                    "timeInForce": "GTC",
                    "leavesValue": "25",
                    "updatedTime": "1725245568704",
                    "side": "Sell",
                    "smpGroup": "0",
                    "triggerPrice": "",
                    "tpLimitPrice": "0",
                    "cumExecFee": "0",
                    "leavesQty": "0.01",
                    "slTriggerBy": "",
                    "closeOnTrigger": false,
                    "placeType": "",
                    "cumExecQty": "0",
                    "reduceOnly": false,
                    "qty": "0.01",
                    "stopLoss": "",
                    "marketUnit": "",
                    "smpOrderId": "",
                    "triggerBy": ""
                }
                ]
        """
        try:
            if symbol:
                open_orders = self.exchange.get_open_orders(symbol=symbol.upper() + 'USDT', category='linear')['result']['list']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': symbol,
                        'direction': i['side'].lower(),
                        'order_type': i['orderType'].lower(),
                        'amount': i['qty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': i['leavesQty'],
                    } for i in open_orders
                ]
            else:
                open_orders = self.exchange.get_open_orders(category='linear', settleCoin='USDT')['result']['list']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['orderType'],
                        'amount': i['qty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': i['leavesQty'],
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.exchange.get_open_orders(symbol=symbol.upper() + 'USDT', category='linear')['result']['list'][0]['orderId']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
            {'orderId': 'a5e2fc55-92af-4e01-bd92-4505e655b698', 'orderLinkId': ''}
        """
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', orderType='Limit', price=str(price), qty=str(amount), reduceOnly=True)['result']
                else:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', orderType='Limit', price=str(price), qty=str(amount))['result']
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', orderType='Market', qty=str(amount), reduceOnly=True)['result']
                else:
                    order_info = self.exchange.place_order(symbol=symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', orderType='Market', qty=str(amount))['result']
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取swap订单详情
    def get_swap_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
            "symbol": "ETHUSDT",
            "orderType": "Limit",
            "orderLinkId": "",
            "slLimitPrice": "0",
            "orderId": "2e229c1a-eeba-4405-b99f-8cfa9e03bee0",
            "cancelType": "UNKNOWN",
            "avgPrice": "",
            "stopOrderType": "",
            "lastPriceOnCreated": "2519.43",
            "orderStatus": "New",
            "createType": "CreateByUser",
            "takeProfit": "",
            "cumExecValue": "0",
            "tpslMode": "",
            "smpType": "None",
            "triggerDirection": "0",
            "blockTradeId": "",
            "isLeverage": "",
            "rejectReason": "EC_NoError",
            "price": "2600",
            "orderIv": "",
            "createdTime": "1725278732259",
            "tpTriggerBy": "",
            "positionIdx": "0",
            "timeInForce": "GTC",
            "leavesValue": "26",
            "updatedTime": "1725278732261",
            "side": "Sell",
            "smpGroup": "0",
            "triggerPrice": "",
            "tpLimitPrice": "0",
            "cumExecFee": "0",
            "leavesQty": "0.01",
            "slTriggerBy": "",
            "closeOnTrigger": false,
            "placeType": "",
            "cumExecQty": "0",
            "reduceOnly": false,
            "qty": "0.01",
            "stopLoss": "",
            "marketUnit": "",
            "smpOrderId": "",
            "triggerBy": ""
            }
        """
        try:
            order_info = self.exchange.get_open_orders(symbol=symbol.upper() + 'USDT', category='linear', orderId=order_id)['result']['list'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['orderType'].lower(),
                'amount': order_info['qty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': order_info['leavesQty'],
                'fee': order_info['cumExecFee']     # usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {'orderId': '2e229c1a-eeba-4405-b99f-8cfa9e03bee0', 'orderLinkId': ''}
        """
        try:
            cancel_order_info = self.exchange.cancel_order(symbol=symbol.upper() + 'USDT', category='linear', orderId=order_id)['result']
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['orderId'] else 'failed',
            }
        except:
            print(format_exc())
            return None

    # region =======================================================小额报价的meme coin
    # 获取最新价格
    def get_swap_latest_price_kcoin(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['lastPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1_kcoin(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['bid1Price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1_kcoin(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['ask1Price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口最优挂单
    def get_swap_best_orderbook_kcoin(self, symbol):
        try:
            raw_orderbook = self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]
            return {
                'bid': [float(raw_orderbook['bid1Price']), float(raw_orderbook['bid1Size'])],
                'ask': [float(raw_orderbook['ask1Price']), float(raw_orderbook['ask1Size'])]
            }
        except:
            print(format_exc())
            return None

    # 获取swap盘口
    def get_swap_orderbook_kcoin(self, symbol, limit=5):
        try:
            raw_orderbook = self.exchange.get_orderbook(symbol='1000' + symbol.upper() + 'USDT', category='linear', limit=limit)['result']
            return {
                'symbol': '1000' + symbol,
                'bid': [raw_orderbook['b']],
                'ask': [raw_orderbook['a']]
            }
        except:
            print(format_exc())
            return None
    
    # 获取永续合约资金费率
    def get_swap_contract_funding_rate_kcoin(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['fundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费率
    def get_swap_contract_history_funding_rate_kcoin(self, symbol, limit=5):
        try:
            funding_rates = self.exchange.get_funding_rate_history(symbol='1000' + symbol.upper() + 'USDT', category='linear', limit=limit)['result']['list']
            return {
                'symbol': '1000' + symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingRateTimestamp'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest_kcoin(self, symbol):
        try:
            return float(self.exchange.get_tickers(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['openInterestValue'])
        except:
            print(format_exc())
            return None

    # TODO：获取swap-kcoin线
    def get_swap_kline_kcoin(self, symbol, period='15m', start='', end='', limit=100):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info_kcoin(self, symbol):
        """
        {
            category: "linear",
            list: [
                {
                symbol: "1000BONKUSDT",
                contractType: "LinearPerpetual",
                status: "Trading",
                baseCoin: "1000BONK",
                quoteCoin: "USDT",
                launchTime: "1672971039000",
                deliveryTime: "0",
                deliveryFeeRate: "",
                priceScale: "7",
                leverageFilter: {
                    minLeverage: "1",
                    maxLeverage: "50.00",
                    leverageStep: "0.01"
                },
                priceFilter: {
                    minPrice: "0.0000010",
                    maxPrice: "19.9999980",
                    tickSize: "0.0000010"
                },
                lotSizeFilter: {
                    maxOrderQty: "101600000",
                    minOrderQty: "100",
                    qtyStep: "100",
                    postOnlyMaxOrderQty: "101600000",
                    maxMktOrderQty: "12090000",
                    minNotionalValue: "5"
                },
                unifiedMarginTrade: True,
                fundingInterval: 240,
                settleCoin: "USDT",
                copyTrading: "both",
                upperFundingRate: "0.01",
                lowerFundingRate: "-0.01",
                isPreListing: False,
                preListingInfo: None,
                riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
                displayName: ""
                }
            ],
            nextPageCursor: ""
        }
        """
        try:
            return self.exchange.get_instruments_info(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']
        except:
            print(format_exc())
            return None

    # 获取永续合约价格最小变动
    def get_swap_order_price_tick_size_kcoin(self, symbol):
        try:
            return int(round(log(1 / float(self.exchange.get_instruments_info(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['priceFilter']['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size_kcoin(self, symbol):
        try:
            return max(int(round(log(1 / float(self.exchange.get_instruments_info(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['lotSizeFilter']['qtyStep']), 10), 0)), 0)
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount_kcoin(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.get_instruments_info(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]
            _min_amount = float(_info['lotSizeFilter']['minOrderQty'])
            _min_nominal = float(_info['lotSizeFilter']['minNotionalValue'])
            _price = self.get_swap_latest_price_kcoin(symbol)
            _amount_precision = max(int(round(log(1 / float(_info['lotSizeFilter']['qtyStep']), 10), 0)), 0)
            _min_amount2 = ceil(_min_nominal / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            return ceil(max(_min_amount, _min_amount2) / _min_amount) * _min_amount
        except:
            print(format_exc())
            return None

    # 获取swap账户持仓信息
    def get_swap_position_kcoin(self, symbol=None):
        try:
            if symbol:
                positions = self.exchange.get_positions(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list']
                return [
                    {
                        'symbol': '1000' + symbol,
                        'direction': i['side'].lower(),
                        'amount': i['size'],
                        'price': i['avgPrice'],
                        'liquidation_price': i['liqPrice'],
                    } for i in positions
                ]
            else:
                positions = self.exchange.get_positions(category='linear', settleCoin='USDT')['result']['list']
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'amount': i['size'],
                        'price': i['avgPrice'],
                        'liquidation_price': i['liqPrice'],
                    } for i in positions if i['symbol'].startswith('1000')
                ]
        except:
            print(format_exc())
            return None
        
    # 获取swap挂单信息
    def get_swap_open_order_kcoin(self, symbol=None):
        try:
            if symbol:
                open_orders = self.exchange.get_open_orders(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': '1000' + symbol,
                        'direction': i['side'].lower(),
                        'order_type': i['orderType'].lower(),
                        'amount': i['qty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': i['leavesQty'],
                    } for i in open_orders
                ]
            else:
                open_orders = self.exchange.privateGetV5OrderRealtime(
                    params={'category': 'linear'})['result']['list']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['orderType'],
                        'amount': i['qty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': i['leavesQty'],
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id_kcoin(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.exchange.get_open_orders(symbol='1000' + symbol.upper() + 'USDT', category='linear')['result']['list'][0]['orderId']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order_kcoin(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
            {'orderId': 'a5e2fc55-92af-4e01-bd92-4505e655b698', 'orderLinkId': ''}
        """
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.exchange.place_order(symbol='1000' + symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', order_type='Limit', price=str(price), qty=str(amount), reduce_only=True)['result']
                else:
                    order_info = self.exchange.place_order(symbol='1000' + symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', order_type='Limit', price=str(price), qty=str(amount))['result']
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.exchange.place_order(symbol='1000' + symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', order_type='Market', qty=str(amount), reduce_only=True)['result']
                else:
                    order_info = self.exchange.place_order(symbol='1000' + symbol.upper() + 'USDT', category='linear', side='Buy' if direction == 'buy' else 'Sell', order_type='Market', qty=str(amount))['result']
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': '1000' + symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取swap订单详情
    def get_swap_order_info_kcoin(self, symbol, order_id):
        try:
            order_info = self.exchange.get_open_orders(symbol='1000' + symbol.upper() + 'USDT', category='linear', orderId=order_id)['result']['list']
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': '1000' + symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['orderType'].lower(),
                'amount': order_info['qty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': order_info['leavesQty'],
                'fee': order_info['cumExecFee']     # usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order_kcoin(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {'orderId': '2e229c1a-eeba-4405-b99f-8cfa9e03bee0', 'orderLinkId': ''}
        """
        try:
            cancel_order_info = self.exchange.cancel_order(symbol='1000' + symbol.upper() + 'USDT', category='linear', order_id=order_id)['result']
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': '1000' + symbol,
                'status': 'canceled' if cancel_order_info['orderId'] else 'failed',
            }
        except:
            print(format_exc())
            return None


    # endregion ============================================usdt swap

    # TODO：币本位合约
    # region ==============================================coin swap

    # endregion


# a = BybitAdapter()
# print(a.cancel_swap_order_kcoin('pepe', '5c6a4aed-fe19-482b-a6fb-8591925c3319'))
# print('aaaa')
# exit()