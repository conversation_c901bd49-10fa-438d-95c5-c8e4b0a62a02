# !/usr/bin/env python
# -*- coding:utf-8 -*-
# region ==============================================备注说明
"""
TODO:
1.
"""
# endregion


# region ==============================================import
from time import time, sleep
from abc import ABC
from datetime import timedelta, datetime
from traceback import format_exc
from math import log, ceil
import sys
sys.path.extend(['/home/<USER>/pycharmproject/kzmfunction/'])


# import pandas as pd


# endregion


# region ==============================================工具函数

# endregion


# region ==============================================交易所class
class ExchangeAdapter(ABC):
    # 本地环境下出现错误可设置此代理
    def proxies_setting(self, proxies={'https': '127.0.0.1:7890'}):
        """
        本地环境下出现错误可设置此代理
        :param proxies:
        """
        # self.exchange.proxies = {'https': '172.20.10.2:6152'}
        self.exchange.proxies = proxies

    # 根据value获取字典的key，用于统一交易所返回的订单状态status
    def get_key_by_value(self, d, value):
        for key, val in d.items():
            if isinstance(val, list) and value in val:
                return key
        return None

    # 返回列表中符合条件的元素的索引
    def find_index(self, l, condition):
        for index, element in enumerate(l):
            if condition(element):
                return index



# a = BinanceAdapter()
# a = HyperliquidAdapter()
# a = ApexproAdapter()
# a = BitgetAdapter()
# a = GateioAdapter()
# a = OkxAdapter()
# a = VertexAdapter()
# a = BybitAdapter()
# a = KucoinAdapter()
# a.proxies_setting()
# b = a.get_swap_account_single_asset('usdt')
# b = a.get_spot_order_info2('eth', 'btc', '')
# b = a.get_swap_order_info('eth', '0x186fc149a7fc8de320fe39e68d3187ee7aaba2c44dfdedbb893d0255df45449d')
# b = a.get_swap_open_order('matic')
# b = a.cancel_swap_order('eth', '1210171820464578565')
# b = a.get_swap_position('sol')
# b = a.get_swap_position()
# b = a.place_spot_order2('eth', 'btc', 'sell', 0.0014, order_type='limit', price=0.044)
# b = a.place_spot_order2('eth', 'btc', 'sell', 0.0038, 0.044, order_type='limit')
# b = a.place_swap_order('eth', 'buy', 0.1, order_type='market', close_position=True)
# b = a.get_spot_order_info2('eth', 'btc', '1740702858627104768')
# b = a.get_spot_order_info('eth', '1210001155527749658')
# b = a.get_spot_order_price_tick_size('btc')
# b = a.cancel_spot_order('eth', '1210002806833623041')
# print(b)