# region ============================================================备注说明
"""
备注说明：
    官方文档：https://www.okx.com/zh-hans/web3/build/docs/api
    非官方sdk：https://github.com/okxapi/python-okx
    原版本安装代码：
    pip install python-okx
    pip install python-okx --upgrade
    目前版本：0.3.9
    新版本安装代码：
    pip install git+https://github.com/kewell2000/python-okx.git
    目前版本：0.3.9
    修改了部分接口，添加了get_flexible_loan_info等接口
    
"""
# endregion ============================================================备注说明


# region ============================================================import
from traceback import format_exc
import sys
import os
from math import log, ceil
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .ExchangeAdapter import ExchangeAdapter
from .api_config import okx_api_config
from okx.MarketData import MarketAPI
from okx.Funding import FundingAPI
from okx.Trade import TradeAPI
from okx.Account import AccountAPI
from okx.PublicData import PublicAPI
from time import time
from datetime import timedelta
import pandas as pd

# endregion ============================================================import


class OkxAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'): 
        self._api_config = okx_api_config.api_config
        self.account = account
        self.exchange_name = 'okx'
        self.broker_id = '91b45db660c6BCDE'     # 邢不行broker_id
        # 创建交易所
        self.market_exchange = MarketAPI(flag='0')
        self.public_exchange = PublicAPI(flag='0')
        self.account_exchange = AccountAPI(flag='0')

        if account:
            self.funding_exchange = FundingAPI(api_key=self._api_config[account]['api_key'], api_secret_key=self._api_config[account]['secret_key'], passphrase=self._api_config[account]['passphrase'], flag='0')
            self.account_exchange = AccountAPI(api_key=self._api_config[account]['api_key'], api_secret_key=self._api_config[account]['secret_key'], passphrase=self._api_config[account]['passphrase'], flag='0')
            self.trade_exchange = TradeAPI(api_key=self._api_config[account]['api_key'], api_secret_key=self._api_config[account]['secret_key'], passphrase=self._api_config[account]['passphrase'], flag='0')

    # region ==============================================spot
    # 获取spot最新成交价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT')['data'][0]['last'])

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:

        """
        try:
            return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT')['data'][0]['bidPx'])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:

        """
        try:
            return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT')['data'][0]['askPx'])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
            {
              "instType": "SPOT",
              "instId": "BTC-USDT",
              "last": "60731.9",
              "lastSz": "0.01253097",
              "askPx": "60732",
              "askSz": "0.57005531",
              "bidPx": "60731.9",
              "bidSz": "0.6681182",
              "open24h": "60828",
              "high24h": "61401.2",
              "low24h": "59667.6",
              "volCcy24h": "424991346.435704982",
              "vol24h": "7007.33692208",
              "ts": "1724374791704",
              "sodUtc0": "60381.4",
              "sodUtc8": "60404"
            }
        """
        try:
            raw_orderbook = self.market_exchange.get_ticker(symbol.upper() + '-USDT')['data'][0]
            return {
                'bid': [raw_orderbook['bidPx'], raw_orderbook['bidSz']],
                'ask': [raw_orderbook['askPx'], raw_orderbook['askSz']]
            }
        except:
            print(format_exc())
            return None

    # 获取spot orderbook
    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "asks": [
            [
              "60806.1",    # 数量
              "0.39915396", # 价格
              "0",  # 作废字段
              "8"  # 订单数
            ],
            [
              "60808",
              "0.0768",
              "0",
              "1"
            ],
            [
              "60808.1",
              "0.0504",
              "0",
              "2"
            ],
            ...
          ],
          "bids": [
            [
              "60806",
              "0.442741",
              "0",
              "7"
            ],
            [
              "60805.3",
              "0.10500181",
              "0",
              "1"
            ],
            [
              "60805.2",
              "0.175003",
              "0",
              "1"
            ],
            ...
          ],
          "ts": "1724375221550"
        }
        """
        try:
            raw_orderbook = self.market_exchange.get_orderbook(symbol.upper() + '-USDT', limit)['data'][0]
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # TODO：获取spot kline
    def get_spot_kline(self):
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            alias: "",
            baseCcy: "ZK",
            category: "1",
            ctMult: "",
            ctType: "",
            ctVal: "",
            ctValCcy: "",
            expTime: "",
            instFamily: "",
            instId: "ZK-USDT",
            instType: "SPOT",
            lever: "5",
            listTime: "1718690928000",
            lotSz: "0.01",
            maxIcebergSz: "9999999999999999.****************",
            maxLmtAmt: "20000000",
            maxLmtSz: "9999999999999999",
            maxMktAmt: "1000000",
            maxMktSz: "1000000",
            maxStopSz: "1000000",
            maxTriggerSz: "9999999999999999.****************",
            maxTwapSz: "9999999999999999.****************",
            minSz: "10",
            optType: "",
            quoteCcy: "USDT",
            ruleType: "normal",
            settleCcy: "",
            state: "live",
            stk: "",
            tickSz: "0.0001",
            uly: ""
            }
        """
        try:
            return self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-USDT')['data'][0]
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            tick_sz = self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-USDT')['data'][0]['tickSz']
            return len(tick_sz.split('.')[1]) if '.' in tick_sz else 0
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-USDT')['data'][0]['lotSz']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            tick_sz = self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-{base_symbol.upper()}')['data'][0]['tickSz']
            return len(tick_sz.split('.')[1]) if '.' in tick_sz else 0      
        except:
            print(format_exc())
            return None     

    # 获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-{base_symbol.upper()}')['data'][0]['lotSz']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            return float(self.public_exchange.get_instruments(instType='SPOT', instId=f'{symbol.upper()}-USDT')['data'][0]['minSz'])
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """`

        :return:
        """
        try:
            symbol_list = []
            _info = self.public_exchange.get_instruments(instType='SPOT')['data']
            symbol_list.extend([i['instId'].lower()
                               for i in _info if i['state'] == 'live'])
            base_symbol_len = len(base_symbol)
            symbol_list = [
                i.replace('-', '') for i in symbol_list if i[-base_symbol_len:] == base_symbol]
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取资金账户余额
    def get_account(self, symbol):
        """
        返回资金账户余额（币或USDT）
        @param symbol:  btc
        @return:
            [{'availBal': '0.********', 'bal': '0.********',
                'ccy': 'ETH', 'frozenBal': '0'}]
        """
        return float(self.funding_exchange.get_balances(ccy=symbol.upper())['data'][0]['availBal'])

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        {
            adjEq: "0.****************",
            availEq: "0.****************",
            borrowFroz: "0",
            details: [
                {
                accAvgPx: "",
                availBal: "0.****************",
                availEq: "0.****************",
                borrowFroz: "0",
                cashBal: "0.****************",
                ccy: "USDC",
                clSpotInUseAmt: "",
                colBorrAutoConversion: "0",
                collateralEnabled: True,
                collateralRestrict: False,
                crossLiab: "0",
                disEq: "0.****************",
                eq: "0.****************",
                eqUsd: "0.****************",
                fixedBal: "0",
                frozenBal: "0",
                imr: "",
                interest: "0",
                isoEq: "0",
                isoLiab: "0",
                isoUpl: "0",
                liab: "0",
                maxLoan: "1.5429024887791956",
                maxSpotInUse: "",
                mgnRatio: "",
                mmr: "",
                notionalLever: "",
                openAvgPx: "",
                ordFrozen: "0",
                rewardBal: "",
                smtSyncEq: "0",
                spotBal: "",
                spotCopyTradingEq: "0",
                spotInUseAmt: "",
                spotIsoBal: "0",
                spotUpl: "",
                spotUplRatio: "",
                stgyEq: "0",
                totalPnl: "",
                totalPnlRatio: "",
                twap: "0",
                uTime: "1749437484856",
                upl: "0",
                uplLiab: "0"
                }
            ],
            imr: "0",
            isoEq: "0",
            mgnRatio: "",
            mmr: "0",
            notionalUsd: "0",
            notionalUsdForBorrow: "0",
            notionalUsdForFutures: "0",
            notionalUsdForOption: "0",
            notionalUsdForSwap: "0",
            ordFroz: "0",
            totalEq: "0.****************",
            uTime: "*************",
            upl: "0"
            }
        """
        try:
            info = self.account_exchange.get_account_balance(ccy=symbol.upper())['data'][0]['details']
            if info:
                return float(info[0]['cashBal'])
            else:
                return 0
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
            {
              "btc": "0.********",
              "bnb": "51.********",
              "usdt": "1498.********",
              "usdc": "0.********",
              "doge": "6925.********",
              "wif": "96802.********"
            }
        """
        try:
            balance = self.account_exchange.get_account_balance()['data'][0]['details']
            return {i['ccy'].lower(): i['cashBal'] for i in balance}
        except:
            print(format_exc())
            return None

    # 获取spot账户挂单信息
    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
          {
            "accFillSz": "0",
            "algoClOrdId": "",
            "algoId": "",
            "attachAlgoClOrdId": "",
            "attachAlgoOrds": [],
            "avgPx": "",
            "cTime": "*************",
            "cancelSource": "",
            "cancelSourceReason": "",
            "category": "normal",
            "ccy": "",
            "clOrdId": "",
            "fee": "0",
            "feeCcy": "USDT",
            "fillPx": "",
            "fillSz": "0",
            "fillTime": "",
            "instId": "ETH-USDT",
            "instType": "SPOT",
            "isTpLimit": "false",
            "lever": "5",
            "linkedAlgoOrd": {
              "algoId": ""
            },
            "ordId": "1740621154558263296",
            "ordType": "limit",
            "pnl": "0",
            "posSide": "net",
            "px": "2700",
            "pxType": "",
            "pxUsd": "",
            "pxVol": "",
            "quickMgnType": "",
            "rebate": "0",
            "rebateCcy": "ETH",
            "reduceOnly": "false",
            "side": "sell",
            "slOrdPx": "",
            "slTriggerPx": "",
            "slTriggerPxType": "",
            "source": "",
            "state": "live",
            "stpId": "",
            "stpMode": "cancel_maker",
            "sz": "0.001",
            "tag": "",
            "tdMode": "cross",
            "tgtCcy": "",
            "tpOrdPx": "",
            "tpTriggerPx": "",
            "tpTriggerPxType": "",
            "tradeId": "",
            "uTime": "*************"
          }
        """
        try:
            open_orders = self.trade_exchange.get_order_list(instType='SPOT', instId=symbol.upper() + '-USDT')['data']
            return [{
                'order_id': order['ordId'],
                'symbol': symbol,
                'direction': order['side'],
                'amount': order['sz'],
                'price': order['px'],
                'order_type': order['ordType'],
                'average_price': order['avgPx'],
                'remain_amount': '',
            } for order in open_orders]
        except:
            print(format_exc())
            return None

    # 下单
    def place_spot_order(self, symbol, direction, amount=None, price=None, order_type='limit', quoteorderqty=None):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param params:
        :return:
            {'clOrdId': '', 'ordId': '1740665914593492992', 'sCode': '0',
                'sMsg': 'Order placed', 'tag': '', 'ts': '1724378286755'}
        """
        try:
            if order_type == 'limit':
                order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT', tdMode='cross', side=direction, ordType='limit', px=str(price), sz=str(amount), tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            elif order_type == 'market':
                # 按拟成交金额下单
                if quoteorderqty:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT', tdMode='cross', side=direction, ordType='market', sz=str(quoteorderqty), tgtCcy='quote_ccy', tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
                else:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT', tdMode='cross', side=direction, ordType='market', sz=str(amount), tgtCcy='base_ccy', tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['ordId'],
                'symbol': symbol,
                'direction': direction,
                # 在市价单传了quoteorderqty情况下，amount表示quoteorderqty，这里amount显示的数据是quoteorderqty(usdt)
                'amount': amount if amount else quoteorderqty,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 下单2
    def place_spot_order2(self, symbol, base_symbol, direction, amount, price='', order_type='limit', quoteorderqty=None):
        try:
            if order_type == 'limit':
                order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-' + base_symbol.upper(), tdMode='cross', side=direction, ordType='limit', px=str(price), sz=str(amount), tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            elif order_type == 'market':
                # 按拟成交金额下单
                if quoteorderqty:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-' + base_symbol.upper(), tdMode='cross', side=direction, ordType='market', sz=str(quoteorderqty), tgtCcy='quote_ccy', tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
                else:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-' + base_symbol.upper(), tdMode='cross', side=direction, ordType='market', sz=str(amount), tgtCcy='base_ccy', tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['ordId'],
                'symbol': symbol,
                'direction': direction,
                # 在市价单传了quoteorderqty情况下，amount表示quoteorderqty，这里amount显示的数据是quoteorderqty(usdt)
                'amount': amount if amount else quoteorderqty,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_spot_order_info(self, symbol, order_id):
        """
            {
                accFillSz: "0.0001",
                algoClOrdId: "",
                algoId: "",
                attachAlgoClOrdId: "",
                attachAlgoOrds: [],
                avgPx: "2479.09",
                cTime: "1749449565115",
                cancelSource: "",
                cancelSourceReason: "",
                category: "normal",
                ccy: "",
                clOrdId: "",
                fee: "-0.00000004",
                feeCcy: "ETH",
                fillPx: "2479.09",
                fillSz: "0.0001",
                fillTime: "1749449565116",
                instId: "ETH-USDT",
                instType: "SPOT",
                isTpLimit: "false",
                lever: "5",
                linkedAlgoOrd: { algoId: "" },
                ordId: "2581918419476914176",
                ordType: "market",
                pnl: "0",
                posSide: "net",
                px: "",
                pxType: "",
                pxUsd: "",
                pxVol: "",
                quickMgnType: "",
                rebate: "0",
                rebateCcy: "USDT",
                reduceOnly: "false",
                side: "buy",
                slOrdPx: "",
                slTriggerPx: "",
                slTriggerPxType: "",
                source: "",
                state: "filled",
                stpId: "",
                stpMode: "cancel_maker",
                sz: "0.0001",
                tag: "",
                tdMode: "cross",
                tgtCcy: "base_ccy",
                tpOrdPx: "",
                tpTriggerPx: "",
                tpTriggerPxType: "",
                tradeId: "598373791",
                uTime: "1749449565118"
            }
        """
        try:
            order_info = self.trade_exchange.get_order(instId=symbol.upper() + '-USDT', ordId=order_id)['data'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['ordId'],
                'symbol': symbol,
                'direction': order_info['side'],
                'order_type': order_info['ordType'],
                'amount': order_info['sz'],
                'price': order_info['px'],
                'average_price': order_info['avgPx'],
                'remain_amount': float(order_info['sz']) - float(order_info['accFillSz']),
                'fee': order_info['fee']    # 买入时，fee为币，卖出时，fee为usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息2
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            order_info = self.trade_exchange.get_order(instId=symbol.upper() + '-' + base_symbol.upper(), ordId=order_id)['data'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['ordId'],
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info['side'],
                'order_type': order_info['ordType'],
                'amount': order_info['sz'],
                'price': order_info['px'],
                'average_price': order_info['avgPx'],
                'remain_amount': float(order_info['sz']) - float(order_info['accFillSz']),
                'fee': order_info['fee']
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_spot_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
            "code":"0",
            "msg":"",
            "data":[
                {
                    "clOrdId":"oktswap6",
                    "ordId":"12345689",
                    "ts":"1695190491421",
                    "sCode":"0",
                    "sMsg":""
                }
            ],
            "inTime": "1695190491421339",
            "outTime": "1695190491423240"
        }
        """
        try:
            order_info = self.trade_exchange.cancel_order(instId=symbol.upper() + '-USDT', ordId=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['code'] == '0' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO 交割合约
    # region ==============================================future
    # TODO：获取交割合约信息
    def get_future_instruments_info(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        :param symbol:
        :param margin_type: 币本位coin or U本位usdt
        :param contract_type: this_week, next_week, quarter, next_quarter
        :return:
            {
                'alias': 'this_week',
                'baseCcy': '',
                'category': '1', 手续费档位，每个交易产品属于哪个档位手续费
                'ctMult': '1',  合约乘数，仅适用于交割/永续/期权
                'ctType': 'linear',
                'ctVal': '0.1', 合约面值，仅适用于交割/永续/期权
                'ctValCcy': 'ETH',  合约面值计价币种，仅适用于交割/永续/期权
                'expTime': '1633075200000',
                'instId': 'ETH-USDT-211001',
                'instType': 'FUTURES',
                'lever': '125',
                'listTime': '1631866200880',
                'lotSz': '1',   下单数量精度，如 BTC-USDT-SWAP：1
                'minSz': '1',   最小下单数量
                'optType': '',
                'quoteCcy': '',
                'settleCcy': 'USDT',    盈亏结算和保证金币种，如 BTC 仅适用于交割/永续/期权
                'state': 'live',
                'stk': '',
                'tickSz': '0.01',   下单价格精度，如 0.0001
                'uly': 'ETH-USDT'
            }
        """
        instId = self.get_futures_instrument_id(
            symbol, margin_type=margin_type, contract_type=contract_type)
        info = self.exchange.get_instruments(
            instType='FUTURES', instId=instId)['data'][0]

        return info

    # TODO：获取合约当期instId
    def get_futures_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.exchange.get_instruments(instType='FUTURES')[
            'data']

        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['uly'] == symbol.upper() + '-' + margin_type2 and i['alias'] == contract_type:
                    return i['instId']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['uly'] == symbol.upper() + '-' + margin_type2:
                    instrument_id_list.append(i['instId'])
            return instrument_id_list

    # TODO：获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.get_futures_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['bidPx'])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['bidPx'])

    # TODO：获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, next_quarter
        @return:
        """
        instrument_id = self.get_futures_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['askPx'])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_ticker(instId=instrument_id)['data'][0]['askPx'])

    # TODO：获取future-K线
    def get_future_kline(self, symbol, period='15m', start='', end='', limit=100, margin_type='usdt', contract_type='quarter'):
        """

        @param symbol:  btc
        @param period:  时间周期,如 [1m/3m/5m/15m/30m/1H/2H/4H/6H/12H/1D/1W/1M/3M/6M/1Y]
        @param start:  str
        @param end:   str
        @param margin_type:   str
        @param contract_type:   str
        @param limit: float 最大100，默认100
        @return:    spot：# 默认100根K线
        """
        # 获取起始时间
        if start and end:
            start = datetimeConvertToMs((pd.to_datetime(start)))
            end = datetimeConvertToMs(pd.to_datetime(end))
        # =======================================现货K线
        instId = self.get_futures_instrument_id(
            symbol, margin_type=margin_type, contract_type=contract_type)

        kline = self.exchange.get_candlesticks(
            instId=instId, bar=period, before=start, after=end, limit=limit)
        # 转为Dataframe，转为北京时区
        kline = pd.DataFrame(kline['data'], columns=[
                             'candle_begin_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy'])
        kline['candle_begin_time'] = pd.to_datetime(
            kline['candle_begin_time'], unit='ms') + timedelta(hours=8)
        kline.sort_values(by='candle_begin_time', ascending=True, inplace=True)
        kline.reset_index(drop=True, inplace=True)

        return kline

    # # TODO：获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # <editor-fold desc="# ====================== TODO： future">


    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion


    # region ==============================================usdt swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT-SWAP')['data'][0]['last'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT-SWAP')['data'][0]['bidPx'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.market_exchange.get_ticker(symbol.upper() + '-USDT-SWAP')['data'][0]['askPx'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
            {
              "instType": "SWAP",
              "instId": "ETH-USDT-SWAP",
              "last": "2639.2",
              "lastSz": "0.2",
              "askPx": "2639.2",
              "askSz": "1723.4",
              "bidPx": "2639.19",
              "bidSz": "47.2",
              "open24h": "2618.26",
              "high24h": "2653.33",
              "low24h": "2582.88",
              "volCcy24h": "1363706.7",
              "vol24h": "13637067",
              "ts": "1724380638414",
              "sodUtc0": "2622.86",
              "sodUtc8": "2599.5"
            }
        """
        try:
            raw_orderbook = self.market_exchange.get_ticker(symbol.upper() + '-USDT-SWAP')['data'][0]
            return {
                'bid': [raw_orderbook['bidPx'], raw_orderbook['bidSz']],
                'ask': [raw_orderbook['askPx'], raw_orderbook['askSz']]
            }
        except:
            print(format_exc())
            return None

    # 获取swap盘口
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
            {
              "asks": [
                [
                  "2638.09",
                  "183.7",
                  "0",
                  "15"
                ],
                [
                  "2638.19",
                  "37.5",
                  "0",
                  "2"
                ],
                [
                  "2638.2",
                  "22.8",
                  "0",
                  "3"
                ]
              ],
              "bids": [
                [
                  "2638.08",
                  "1044.8",
                  "0",
                  "27"
                ],
                [
                  "2638.07",
                  "3.2",
                  "0",
                  "2"
                ],
                [
                  "2638.02",
                  "20.8",
                  "0",
                  "2"
                ]
              ],
              "ts": "1724381077909"
            }
        """
        try:
            raw_orderbook = self.market_exchange.get_orderbook(symbol.upper() + '-USDT-SWAP', limit)['data'][0]
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        {
          "fundingRate": "0.0000314352602773",
          "fundingTime": "17244********",
          "instId": "ETH-USDT-SWAP",
          "instType": "SWAP",
          "maxFundingRate": "0.0075",
          "method": "current_period",
          "minFundingRate": "-0.0075",
          "nextFundingRate": "",
          "nextFundingTime": "1724428800000",
          "premium": "0.0000853808936534",
          "settFundingRate": "0.0000433935815389",
          "settState": "settled",
          "ts": "1724382305047"
        }
        """
        try:
            return float(self.public_exchange.get_funding_rate(symbol.upper() + '-USDT-SWAP')['data'][0]['fundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
        [
          {
            "fundingRate": "-0.000005860007631",
            "fundingTime": "1724256000000",
            "instId": "ETH-USDT-SWAP",
            "instType": "SWAP",
            "method": "current_period",
            "realizedRate": "-0.000005860007631"
          },
          {
            "fundingRate": "0.0000038240682918",
            "fundingTime": "1724284800000",
            "instId": "ETH-USDT-SWAP",
            "instType": "SWAP",
            "method": "current_period",
            "realizedRate": "0.0000038240682918"
          },
          {
            "fundingRate": "0.0000078173586184",
            "fundingTime": "1724313600000",
            "instId": "ETH-USDT-SWAP",
            "instType": "SWAP",
            "method": "current_period",
            "realizedRate": "0.0000078173586184"
          },
          ...
        ]
        """
        try:
            funding_rates = self.public_exchange.funding_rate_history(symbol.upper() + '-USDT-SWAP')['data'][:limit]
            funding_rates.reverse()
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
            {
              "instId": "ETH-USDT-SWAP",
              "instType": "SWAP",
              "oi": "2862131.09999999946",      持仓量（按张折算）
              "oiCcy": "286213.109999999946",   持仓量（按币折算）
              "ts": "1724382896551"
            }
        """
        try:
            return float(self.public_exchange.get_open_interest(instId=symbol.upper() + '-USDT-SWAP', instType='SWAP')['data'][0]['oiUsd'])
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
            {
              "alias": "",
              "baseCcy": "",
              "category": "1",
              "ctMult": "1",
              "ctType": "linear",
              "ctVal": "0.1",
              "ctValCcy": "ETH",
              "expTime": "",
              "instFamily": "ETH-USDT",
              "instId": "ETH-USDT-SWAP",
              "instType": "SWAP",
              "lever": "100",
              "listTime": "1611916828000",
              "lotSz": "0.1",
              "maxIcebergSz": "1********.****************",
              "maxLmtAmt": "20000000",
              "maxLmtSz": "1********",
              "maxMktAmt": "",
              "maxMktSz": "20000",
              "maxStopSz": "20000",
              "maxTriggerSz": "1********.****************",
              "maxTwapSz": "1********.****************",
              "minSz": "0.1",
              "optType": "",
              "quoteCcy": "",
              "ruleType": "normal",
              "settleCcy": "USDT",
              "state": "live",
              "stk": "",
              "tickSz": "0.01",
              "uly": "ETH-USDT"
            }
        """
        try:
            return self.public_exchange.get_instruments(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.public_exchange.get_instruments(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]['tickSz']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            info = self.public_exchange.get_instruments(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]
            return max(int(round(log(1 / (float(info['lotSz']) * float(info['ctVal'])), 10), 0)), 0)
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.public_exchange.get_instruments(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]
            return float(_info['minSz']) * float(_info['ctVal'])
        except:
            print(format_exc())
            return None

    # 单张合约对应的币数量
    def token_amount_per_contract(self, symbol):
        """

        :param symbol:
        :return:
        """
        return float(self.public_exchange.get_instruments(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]['ctVal'])

    # 获取永续合约币对
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:  usdt, usdc
        :return:
        """
        try:
            symbol_list = []
            _info = self.public_exchange.get_instruments(instType='SWAP')['data']
            symbol_list.extend([i['instFamily'].replace('-' + base_symbol.upper(), base_symbol).lower()
                               for i in _info if i['settleCcy'] == base_symbol.upper()])
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        """
        :param symbol:
        :return:
            统一账户
        """
        try:
            info = self.account_exchange.get_account_balance(ccy=symbol.upper())['data'][0]['details']
            if info:
                return float(info[0]['availBal'])
            else:
                return 0
        except:
            print(format_exc())
            return None

    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        :param symbol:
        :return:
            [
              {
                "adl": "1",
                "availPos": "",
                "avgPx": "2635.66",
                "baseBal": "",
                "baseBorrowed": "",
                "baseInterest": "",
                "bePx": "",
                "bizRefId": "",
                "bizRefType": "",
                "cTime": "*************",
                "ccy": "USDT",
                "clSpotInUseAmt": "",
                "closeOrderAlgo": [],
                "deltaBS": "-0.1",
                "deltaPA": "-0.1",
                "fee": "-0.0790698",
                "fundingFee": "0",
                "gammaBS": "",
                "gammaPA": "",
                "idxPx": "2636.32",
                "imr": "51.408239999999985",
                "instId": "ETH-USDT-SWAP",
                "instType": "SWAP",
                "interest": "",
                "last": "2636.26",
                "lever": "0",
                "liab": "",
                "liabCcy": "",
                "liqPenalty": "0",
                "liqPx": "",
                "margin": "",
                "markPx": "2636.29",
                "maxSpotInUseAmt": "",
                "mgnMode": "cross",
                "mgnRatio": "17.71587616338429",
                "mmr": "39.54479999999999",
                "notionalUsd": "263.74236047",
                "optVal": "",
                "pendingCloseOrdLiabVal": "",
                "pnl": "0",
                "pos": "-1",
                "posCcy": "",
                "posId": "1741005611945541632",
                "posSide": "net",
                "quoteBal": "",
                "quoteBorrowed": "",
                "quoteInterest": "",
                "realizedPnl": "-0.0790698",
                "spotInUseAmt": "",
                "spotInUseCcy": "",
                "thetaBS": "",
                "thetaPA": "",
                "tradeId": "1386836211",
                "uTime": "*************",
                "upl": "-0.063********00109",
                "uplLastPx": "-0.06********000364",
                "uplRatio": "-0.****************",
                "uplRatioLastPx": "-0.****************",
                "usdPx": "",
                "vegaBS": "",
                "vegaPA": ""
              }
            ]
        """
        try:
            if symbol:
                token_amount_per_contract = self.token_amount_per_contract(symbol)
                positions = self.account_exchange.get_positions(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data']
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(i['pos']) > 0 else 'sell',
                        'amount': abs(float(i['pos'])) * token_amount_per_contract,
                        'price': i['avgPx'],
                        'liquidation_price': i['liqPx'],
                    } for i in positions
                ]
            else:
                positions = self.account_exchange.get_positions(instType='SWAP')['data']
                return [
                    {
                        'symbol': i['instId'].replace('-USDT-SWAP', '').lower(),
                        'direction': 'buy' if float(i['pos']) > 0 else 'sell',
                        'amount': abs(float(i['pos'])) * self.token_amount_per_contract(i['instId'].replace('-USDT-SWAP', '').lower()),
                        'price': i['avgPx'],
                        'liquidation_price': i['liqPx'],
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
        """
        try:
            return float(self.account_exchange.get_account_balance()['data'][0]['mgnRatio'])
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        :param symbol:
        :return:
        [
          {
            "accFillSz": "0",
            "algoClOrdId": "",
            "algoId": "",
            "attachAlgoClOrdId": "",
            "attachAlgoOrds": [],
            "avgPx": "",
            "cTime": "*************",
            "cancelSource": "",
            "cancelSourceReason": "",
            "category": "normal",
            "ccy": "",
            "clOrdId": "",
            "fee": "0",
            "feeCcy": "USDT",
            "fillPx": "",
            "fillSz": "0",
            "fillTime": "",
            "instId": "ETH-USDT-SWAP",
            "instType": "SWAP",
            "isTpLimit": "false",
            "lever": "",
            "linkedAlgoOrd": {
              "algoId": ""
            },
            "ordId": "1740949550173634560",
            "ordType": "limit",
            "pnl": "0",
            "posSide": "net",
            "px": "2700",
            "pxType": "",
            "pxUsd": "",
            "pxVol": "",
            "quickMgnType": "",
            "rebate": "0",
            "rebateCcy": "USDT",
            "reduceOnly": "false",
            "side": "sell",
            "slOrdPx": "",
            "slTriggerPx": "",
            "slTriggerPxType": "",
            "source": "",
            "state": "live",
            "stpId": "",
            "stpMode": "cancel_maker",
            "sz": "0.1",
            "tag": "",
            "tdMode": "cross",
            "tgtCcy": "",
            "tpOrdPx": "",
            "tpTriggerPx": "",
            "tpTriggerPxType": "",
            "tradeId": "",
            "uTime": "*************"
          }
        ]
        """
        try:
            if symbol:
                open_orders = self.trade_exchange.get_order_list(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data']
                return [
                    {
                        'order_id': i['ordId'],
                        'symbol': symbol,
                        'direction': i['side'],
                        'order_type': i['ordType'],
                        'amount': i['sz'] * self.token_amount_per_contract(symbol),
                        'price': i['px'],
                        'average_price': i['avgPx'],
                        'remain_amount': float(i['sz']) - float(i['accFillSz']),
                    } for i in open_orders
                ]
            else:
                # 不带symbol的权重为40 请小心使用不带symbol参数的调用
                open_orders = self.trade_exchange.get_order_list(instType='SWAP')['data']
                return [
                    {
                        'order_id': i['ordId'],
                        'symbol': i['instId'].replace('-USDT-SWAP', '').lower(),
                        'direction': i['side'],
                        'order_type': i['ordType'],
                        'amount': float(i['sz']) * self.token_amount_per_contract(i['instId'].replace('-USDT-SWAP', '').lower()),
                        'price': i['px'],
                        'average_price': i['avgPx'],
                        'remain_amount': (float(i['sz']) - float(i['accFillSz'])) * self.token_amount_per_contract(i['instId'].replace('-USDT-SWAP', '').lower()),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.trade_exchange.get_order_list(instType='SWAP', instId=symbol.upper() + '-USDT-SWAP')['data'][0]['ordId']
        except:
            print(format_exc())
            return None

    #  下单
    def place_swap_order(self, symbol, direction, amount, price=None, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
            {
              "clOrdId": "",
              "ordId": "1740998526423908352",
              "sCode": "0",
              "sMsg": "Order placed",
              "tag": "",
              "ts": "1724388199360"
            }
        """
        try:
            per_contract_amount = self.token_amount_per_contract(symbol)
            _amount = amount / per_contract_amount
            if order_type == 'limit':
                if close_position:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT-SWAP', tdMode='cross', side=direction, ordType='limit', px=str(price), sz=str(_amount), reduceOnly=True, tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
                else:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT-SWAP', tdMode='cross', side=direction, ordType='limit', px=str(price), sz=str(_amount), tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT-SWAP', tdMode='cross', side=direction, ordType='market', sz=str(_amount), reduceOnly=True, tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
                else:
                    order_info = self.trade_exchange.place_order(instId=symbol.upper() + '-USDT-SWAP', tdMode='cross', side=direction, ordType='market', sz=str(_amount), tag=self.broker_id, clOrdId=str(int(time())))['data'][0]
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['ordId'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取swap订单详情
    def get_swap_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
          "accFillSz": "0",
          "algoClOrdId": "",
          "algoId": "",
          "attachAlgoClOrdId": "",
          "attachAlgoOrds": [],
          "avgPx": "",
          "cTime": "*************",
          "cancelSource": "",
          "cancelSourceReason": "",
          "category": "normal",
          "ccy": "",
          "clOrdId": "",
          "fee": "0",
          "feeCcy": "USDT",
          "fillPx": "",
          "fillSz": "0",
          "fillTime": "",
          "instId": "ETH-USDT-SWAP",
          "instType": "SWAP",
          "isTpLimit": "false",
          "lever": "",
          "linkedAlgoOrd": {
            "algoId": ""
          },
          "ordId": "1740949550173634560",
          "ordType": "limit",
          "pnl": "0",
          "posSide": "net",
          "px": "2700",
          "pxType": "",
          "pxUsd": "",
          "pxVol": "",
          "quickMgnType": "",
          "rebate": "0",
          "rebateCcy": "USDT",
          "reduceOnly": "false",
          "side": "sell",
          "slOrdPx": "",
          "slTriggerPx": "",
          "slTriggerPxType": "",
          "source": "",
          "state": "live",
          "stpId": "",
          "stpMode": "cancel_maker",
          "sz": "0.1",
          "tag": "",
          "tdMode": "cross",
          "tgtCcy": "",
          "tpOrdPx": "",
          "tpTriggerPx": "",
          "tpTriggerPxType": "",
          "tradeId": "",
          "uTime": "*************"
        }
        """
        try:
            order_info = self.trade_exchange.get_order(instId=symbol.upper() + '-USDT-SWAP', ordId=order_id)['data'][0]
            per_contract_amount = self.token_amount_per_contract(symbol)
            return {
                'order_id': order_info['ordId'],
                'symbol': order_info['instId'].replace('-USDT-SWAP', '').lower(),
                'direction': order_info['side'],
                'order_type': order_info['ordType'],
                'amount': float(order_info['sz']) * per_contract_amount,
                'price': order_info['px'],
                'average_price': order_info['avgPx'],
                'remain_amount': (float(order_info['sz']) - float(order_info['accFillSz'])) * per_contract_amount,
                'fee': order_info['fee']    # usdt手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
          "code": "0",
          "data": [
            {
              "clOrdId": "",
              "ordId": "1740960565556789248",
              "sCode": "0",
              "sMsg": "",
              "ts": "1724387603641"
            }
          ],
          "inTime": "1724387603641220",
          "msg": "",
          "outTime": "1724387603642928"
        }
        """
        try:
            cancel_order_info = self.trade_exchange.cancel_order(instId=symbol.upper() + '-USDT-SWAP', ordId=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['code'] == '0' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # TODO：获取资金费率历史
    def get_funding_fee(self, symbol, margin_type='usdt', start_time=None, end_time=None, days=None):
        """

        :param symbol:
        :param margin_type:
        :param start_time:
        :param end_time:
        :param days:
        :return:
        """
        params = {'instId': symbol.upper() + '-USDT-SWAP', 'incomeType': 'FUNDING_FEE'}
        if start_time:
            # 将时间转换为毫秒，并转为utc时间
            start_time = pd.to_datetime(start_time) - timedelta(hours=8)
            params['startTime'] = int(pd.to_datetime(start_time).timestamp() * 1000)
        if end_time:
            params['endTime'] = int(pd.to_datetime(end_time).timestamp() * 1000)
        info = self.exchange.privateGetFundingRateHistory(params=params)
        # 转为dataframe格式，并计算cumFunding，修改时间显示
        df = pd.DataFrame(info)
        if df.empty:
            return df
        df['income'] = df['income'].astype(float)
        df['time'] = pd.to_datetime(pd.to_numeric(df['time']), unit='ms') + timedelta(hours=8)
        df.sort_values(by='time', ascending=False, inplace=True)
        df.reset_index(drop=True, inplace=True)
        if days:
            days_ago = pd.Timestamp.now() - pd.Timedelta(days=days)
            df = df[df['time'] >= days_ago]
        return df
    # endregion

    # TODO：币本位合约
    # region ==============================================coin swap


    # region ==============================================borrow/lending
    # 获取借币利率与限额
    def get_loan_interest_rate(self, token='USDT'):
        """
        获取借币利率与限额
        :param token: 币种，如USDT、USDC等
        :return: 借币利率和限额信息
        """
        try:
            return float(self.account_exchange.get_interest_rate(ccy=token.upper())['data'][0]['interestRate']) *24 * 365
        except:
            print(format_exc())
            return None

    # 获取计息记录(TODO:暂未统一格式)
    def get_interest_accrued(self, token=None, after=None, before=None):
        """
        获取计息记录，支持循环获取所有历史数据
        :param token: 币种
        :param after: 请求此refId之后的数据 (refId字符串)
        :param before: 请求此refId之前的数据 (refId字符串)
        :return: pandas DataFrame格式的计息记录
        """
        try:
            all_data = []
            current_before = None

            # 将时间戳转换为毫秒
            if after and isinstance(after, str):
                after = int(pd.to_datetime(after).timestamp() * 1000) - 8 * 3600 * 1000
            if before and isinstance(before, str):
                before = int(pd.to_datetime(before).timestamp() * 1000) - 8 * 3600 * 1000

            while True:
                # 调用API获取数据
                params = {'ccy': token}
                if current_before:
                    params['before'] = current_before

                # print(f"正在请求数据，参数: {params}")
                result = self.account_exchange.get_flexible_interest_accrued(**params)
                if not result or result.get('code') != '0' or not result.get('data'):
                    # print(f"API请求失败或无数据: {result}")
                    break

                data = result['data']
                # print(f"获取到 {len(data)} 条记录")

                # 过滤数据
                filtered_data = []
                for record in data:
                    record_ts = int(record.get('ts', 0))
                    # 检查时间范围
                    if after and record_ts <= after:
                        # print(f"记录时间戳 {record_ts} <= after {after}，停止获取")
                        break

                    if before and record_ts >= before:
                        # print(f"记录时间戳 {record_ts} >= before {before}，跳过")
                        continue

                    filtered_data.append(record)

                if not filtered_data:
                    # print("没有符合条件的数据，停止获取")
                    break

                all_data.extend(filtered_data)
                # print(f"累计获取 {len(all_data)} 条有效记录")


                # 检查是否达到after时间点
                last_record_ts = int(data[-1].get('ts', 0))
                if after and last_record_ts <= after:
                    break

                # 设置下次请求的before参数为当前批次最后一条记录的refId
                current_before = data[-1].get('refId')
                if not current_before:
                    # print("无法获取refId，停止获取")
                    break


            # 转换为DataFrame
            if all_data:
                df = pd.DataFrame(all_data)

                # 添加时间列
                df['datetime'] = pd.to_datetime(pd.to_numeric(df['ts']), unit='ms') + timedelta(hours=8)

                # 按时间排序（最新的在前）
                df = df.sort_values('ts', ascending=False).reset_index(drop=True)

                return df
            else:
                return pd.DataFrame()

        except Exception as e:
            print(format_exc())
            return None

    # 获取借/还币历史(TODO:暂未统一格式)
    def get_borrow_repay_history(self, token=None, after=None, before=None, limit=100):
        """
        获取借/还币历史，支持循环获取所有历史数据
        :param ccy: 币种
        :param after: 请求此时间戳之后的数据 (Unix时间戳，毫秒)
        :param before: 请求此时间戳之前的数据 (Unix时间戳，毫秒)
        :param limit: 每次请求的数量限制，默认100
        :return: pandas DataFrame格式的借还币历史记录
        """
        try:
            all_data = []
            current_before = None

            # 将时间戳转换为毫秒
            if after and isinstance(after, str):
                after = int(pd.to_datetime(after).timestamp() * 1000)
            if before and isinstance(before, str):
                before = int(pd.to_datetime(before).timestamp() * 1000)

            # print(f"开始获取借币历史数据，币种: {token}, after: {after}, before: {before}")

            while True:
                # 调用API获取数据
                params = {'ccy': token, 'limit': limit}
                if current_before:
                    params['before'] = current_before

                # print(f"正在请求数据，参数: {params}")
                result = self.account_exchange.get_flexible_loan_history(**params)

                if not result or result.get('code') != '0' or not result.get('data'):
                    # print(f"API请求失败或无数据: {result}")
                    break

                data = result['data']
                # print(f"获取到 {len(data)} 条记录")

                # 过滤数据
                filtered_data = []
                for record in data:
                    record_ts = int(record.get('ts', 0))

                    # 检查时间范围
                    if after and record_ts <= after:
                        # print(f"记录时间戳 {record_ts} <= after {after}，停止获取")
                        break

                    if before and record_ts >= before:
                        # print(f"记录时间戳 {record_ts} >= before {before}，跳过")
                        continue

                    filtered_data.append(record)

                if not filtered_data:
                    # print("没有符合条件的数据，停止获取")
                    break

                all_data.extend(filtered_data)
                # print(f"累计获取 {len(all_data)} 条有效记录")

                # 检查是否需要继续获取
                if len(data) < limit:
                    # print("返回数据少于limit，已获取完所有数据")
                    break

                # 检查是否达到after时间点
                last_record_ts = int(data[-1].get('ts', 0))
                if after and last_record_ts <= after:
                    # print(f"已到达after时间点 {after}，停止获取")
                    break

                # 设置下次请求的before参数为当前批次最后一条记录的refId
                current_before = data[-1].get('refId')
                if not current_before:
                    # print("无法获取refId，停止获取")
                    break

                # print(f"设置下次请求的before参数: {current_before}")

            # 转换为DataFrame
            if all_data:
                df = pd.DataFrame(all_data)

                # 添加时间列
                df['datetime'] = pd.to_datetime(pd.to_numeric(df['ts']), unit='ms')
                df['datetime_beijing'] = df['datetime'] + timedelta(hours=8)

                # 按时间排序（最新的在前）
                df = df.sort_values('ts', ascending=False).reset_index(drop=True)

                # print(f"最终获取到 {len(df)} 条借币历史记录")
                # print(f"时间范围: {df['datetime_beijing'].min()} 到 {df['datetime_beijing'].max()}")

                return df
            else:
                # print("未获取到任何数据")
                return pd.DataFrame()

        except Exception as e:
            # print(f"获取借币历史时发生错误: {str(e)}")
            print(format_exc())
            return None

    # 获取借币信息(TODO:暂未统一格式)
    def get_loan_info(self):
        """
        获取借币信息
        :return: 借币信息
        {
            collateralData: [{ amt: "26.********", ccy: "BTC" }],
            collateralNotionalUsd: "3093779.********",
            curLTV: "0.6674",
            liqLTV: "0.9657",
            loanData: [
                { amt: "2063914.********", ccy: "USDT" },
                { amt: "3.********", ccy: "USDC" }
            ],
            loanNotionalUsd: "2064255.********",
            marginCallLTV: "0.8657",
            riskWarningData: { instId: "", liqPx: "" }
            }
        """
        try:
            return self.account_exchange.get_flexible_loan_info()['data'][0]
        except:
            print(format_exc())
            return None

    # TODO：获取交易产品最大可借
    def get_max_loan(self, instId, mgnMode, mgnCcy=None):
        """
        获取交易产品最大可借
        :param instId: 产品ID
        :param mgnMode: 保证金模式
        :param mgnCcy: 保证金币种
        :return: 最大可借信息
        """
        try:
            return self.account_exchange.get_max_loan(instId=instId, mgnMode=mgnMode, mgnCcy=mgnCcy)
        except:
            print(format_exc())
            return None

    # TODO：查看质押币种 (通过账户配置获取)
    def get_collateral_assets(self, ccy=None):
        """
        查看质押币种
        :param ccy: 币种
        :return: 质押币种信息
        """
        try:
            # 通过账户配置获取质押相关信息
            return self.account_exchange.get_account_config()
        except:
            print(format_exc())
            return None

    # TODO：手动借/还币
    def borrow_repay(self, ccy, side, amt, rate=None):
        """
        手动借/还币
        :param ccy: 币种
        :param side: borrow:借币 repay:还币
        :param amt: 借币或还币数量
        :param rate: 借币利率，仅适用于借币
        :return: 操作结果
        """
        try:
            return self.account_exchange.borrow_repay(ccy=ccy, side=side, amt=amt, rate=rate)
        except:
            print(format_exc())
            return None

    # TODO：设置自动还币
    def set_auto_repay(self, autoRepay):
        """
        设置自动还币
        :param autoRepay: true:开启自动还币 false:关闭自动还币
        :return: 设置结果
        """
        try:
            return self.account_exchange.set_auto_repay(autoRepay=autoRepay)
        except:
            print(format_exc())
            return None
    # endregion ==============================================borrow/lending




# a = OkxAdapter(account='查询')
# # b = a.get_spot_order_info('eth', '2581918419476914176')
# # b = a.place_spot_order('eth', 'buy', order_type='market', quoteorderqty=2)
# b = a.get_loan_info()
# print(b)