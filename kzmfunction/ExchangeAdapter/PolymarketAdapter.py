# region =======================================import
from time import sleep
from math import log, ceil
from py_clob_client.constants import AMOY, POLYGON
from py_clob_client.clob_types import ApiCreds, BalanceAllowanceParams, AssetType
from py_clob_client.client import ClobClient
from py_clob_client.clob_types import OrderArgs, OrderType, OpenOrderParams
from py_clob_client.order_builder.constants import BUY, SELL
from api_config import polymarket_api_config
from ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# endregion =======================================import


class PolymarketAdapter(ExchangeAdapter):
    def __init__(self, account='apex对冲-3'):
        self._api_config = polymarket_api_config.api_config
        self.account = account
        self.exchange_name = 'polymarket'
        # 创建交易所客户端
        self.client = ClobClient(
            host='https://clob.polymarket.com',
            key=self._api_config[account]['secret_key'],
            chain_id=POLYGON,
            signature_type=2,
            funder=self._api_config[account]['address'],
            creds=ApiCreds(
                api_key='cd3e2401-d848-4c69-61aa-fd2cd6ce3d31',
                api_secret='Jr48zRnXylygaxyeCvCr6Jgsy7_FjuGGazJaXRdCKGQ=',
                api_passphrase='****************************************************************',
            )
        )
        # print(self.client.derive_api_key())
        # print(self.client.create_api_key())

    # region =======================================market
    # 获取市场信息
    def get_market_info(self, condition_id):
        """
        获取市场信息
        @param condition_id: 市场ID
        @return: 市场信息
        """
        try:
            market = self.client.get_market(condition_id)
            return {
                'condition_id': market['condition_id'],
                'description': market['description'],
                'active': market['active'],
                'tokens': market['tokens'],
                'minimum_order_size': market['minimum_order_size'],
                'minimum_tick_size': market['minimum_tick_size']
            }
        except:
            print(format_exc())
            return None

    # 获取所有市场
    def get_markets(self):
        """
        获取所有可用市场
        @return: 市场列表
        """
        try:
            markets = self.client.get_markets()['data']
            return [
                {
                    'condition_id': market['condition_id'],
                    'description': market['description'],
                    'active': market['active'],
                    'tokens': market['tokens'],
                    'minimum_order_size': market['minimum_order_size'],
                    'minimum_tick_size': market['minimum_tick_size']
                } for market in markets
            ]
        except:
            print(format_exc())
            return None

    # 获取盘口买1价
    def get_buy1(self, token_id):
        """
        返回当前盘口的买1价格
        @param market_id: 市场ID
        @return: 买1价格
        """
        try:
            orderbook = self.client.get_order_book(token_id)
            return float(orderbook.bids[-1].price)
        except:
            print(format_exc())
            return None

    # 获取盘口卖1价
    def get_sell1(self, token_id):
        """
        返回当前盘口的卖1价格
        @param market_id: 市场ID
        @return: 卖1价格
        """
        try:
            orderbook = self.client.get_order_book(token_id)
            return float(orderbook.asks[-1].price)
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_orderbook(self, token_id):
        """
        获取市场的订单簿
        @param condition_id: 市场ID
        @return: 订单簿数据
        OrderBookSummary(
            (market = "0xdd22472e552920b8438158ea7238bfadfa4f736aa4cee91a6b86c39ead110917"),
            (asset_id = "21742633143463906290569050155826241533067272736897614950488156847949938836455"),
            (timestamp = "1729665952023"),
            (bids = [
                OrderSummary((price = "0.001"), (size = "10003710.4")),
                OrderSummary((price = "0.002"), (size = "100722")),
                OrderSummary((price = "0.003"), (size = "101000")),
                OrderSummary((price = "0.005"), (size = "104361.07")),
                OrderSummary((price = "0.007"), (size = "200000")),
                OrderSummary((price = "0.008"), (size = "5000000")),
                OrderSummary((price = "0.009"), (size = "100000")),
                OrderSummary((price = "0.01"), (size = "736")),
                OrderSummary((price = "0.018"), (size = "268.61")),
                OrderSummary((price = "0.02"), (size = "10100")),
                OrderSummary((price = "0.022"), (size = "50000")),
                OrderSummary((price = "0.023"), (size = "20000")),
                ...
            ]),
            (asks = [
                ...
            ])
        )
        """
        try:
            orderbook = self.client.get_order_book(token_id)
            return orderbook
        except:
            print(format_exc())
            return None

    # endregion

    # region =======================================account
    # 获取账户余额(usdc)
    def get_account_balance(self):
        """
        返回账户余额
        @return: 账户余额
            {
            balance: "0",
            allowances: {
                "0x4bFb41d5B3570DeFd03C39a9A4D8dE6Bd8B8982E": "2000000",
                "0xC5d563A36AE78145C45a50134d48A1215220f80a": "0",
                "0xd91E80cF2E7be2e162c6513ceD06f1dD0dA35296": "0"
            }
            }
        """
        try:
            collateral = self.client.get_balance_allowance(params=BalanceAllowanceParams(asset_type=AssetType.COLLATERAL))
            return float(collateral['balance']) / 10 ** 6
        except:
            print(format_exc())
            return None

    # TODO：获取持仓信息
    def get_positions(self, token_id=None):
        """
        返回当前账户的持仓信息
        @return: 持仓信息列表
        """
        try:
            return self.client.update_balance_allowance()
        except:
            print(format_exc())
            return None

    # endregion

    # region =======================================order
    # 下单
    def place_order(self, token_id, side, size, price=None):
        """
        下单
        @param market_id: 市场ID
        @param side: 买卖方向 ('buy' 或 'sell')
        @param size: 下单数量
        @param price: 下单价格 (限价单需要)
        @param order_type: 订单类型 ('limit' 或 'market')
        @return: 订单信息
        {
            errorMsg: "",
            orderID: "0xdd1edc8695fbada30dd0d60262ff23acdd7ab18fee5b38865c84575c8073eacc",
            takingAmount: "1",
            makingAmount: "0.362",
            status: "matched",
            transactionsHashes: [
                "0x7ec82d53994e8b71c8ce2a940299496e380ce92f2efa197dc33d00fd88512723"
            ],
            success: True
            }
        """
        try:
            order_args = OrderArgs(
                price=price,
                size=size,
                side=BUY if side == 'buy' else SELL,
                token_id=token_id
            )
            signed_order = self.client.create_order(order_args)
            return self.client.post_order(signed_order, OrderType.GTC)
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_order_info(self, order_id=''):
        """
        获取订单信息
        @param order_id: 订单ID
        @return: 订单信息
        """
        try:
            return self.client.get_order(order_id)
        except:
            print(format_exc())
            return None

    # 撤单
    def cancel_order(self, order_id):
        """
        撤销订单
        @param order_id: 订单ID
        @return: 撤单结果
        {
            not_canceled: {},
            canceled: [
                "0xe13cbcdf31f9b284a98c7c15940c7e388be1e916b6be09dff43719b577399366"
            ]
            }
        """
        try:
            return self.client.cancel(order_id)
        except:
            print(format_exc())
            return None

    # 获取未成交订单
    def get_open_orders(self, token_id=None):
        """
        获取未成交订单
        @param token_id: 市场ID (可选)
        @return: 未成交订单列表
        [
            {
                id: "0x1651763d4e7ef85bf853fab692b2d8d20aefc057708519def907e795c376d127",
                status: "LIVE",
                owner: "cd3e2401-d848-4c69-61aa-fd2cd6ce3d31",
                maker_address: "0xe49324f5B7e38908F29b8F6a7bB51Ae581af2018",
                market: "0xdd22472e552920b8438158ea7238bfadfa4f736aa4cee91a6b86c39ead110917",
                asset_id: "21742633143463906290569050155826241533067272736897614950488156847949938836455",
                side: "BUY",
                original_size: "5",
                size_matched: "0",
                price: "0.635",
                outcome: "Yes",
                expiration: "0",
                order_type: "GTC",
                associate_trades: [],
                created_at: 1729676517
            }
            ]
        """
        try:
            if token_id:
                return self.client.get_orders(
                    OpenOrderParams(
                        asset_id=token_id
                    )
                )
            else:
                return self.client.get_orders()
        except:
            print(format_exc())
            return None

    # endregion


exchange = PolymarketAdapter()
# a = exchange.get_buy1('48331043336612883890938759509493159234755048973500640148014422747788308965732')
YES_DONALD_TRUMP = '21742633143463906290569050155826241533067272736897614950488156847949938836455'
NO_DONALD_TRUMP = '48331043336612883890938759509493159234755048973500640148014422747788308965732'
YES_KAMALA_HARRIS = '69236923620077691027083946871148646972011131466059644796654161903044970987404'
NO_KAMALA_HARRIS = '87584955359245246404952128082451897287778571240979823316620093987046202296181'
# a = exchange.get_market_info(YES_DONALD_TRUMP)

# a = exchange.place_order(NO_DONALD_TRUMP, 'sell', 1, 0.635)
a = exchange.get_positions()
print(a)
print("Done!")
