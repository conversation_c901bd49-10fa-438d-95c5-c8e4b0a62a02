"""
备注说明：
https://api-docs.pro.apex.exchange/#introduction
https://github.com/ApeX-Protocol/apexpro-openapi#basic-usage
pip install apexpro
要求：
版本：
apexpro 1.1.9以上
python:3.9.7
web3:6.11.0
    
    
"""
# region ==============================================import
from .ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
import sys
import os
from math import log, ceil
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .api_config import apexpro_api_config
from time import time

# endregion ==============================================import


class ApexproAdapter(ExchangeAdapter):
    def __init__(self, account='apex-对冲1'):
        self._api_config = apexpro_api_config.api_config
        self.name = account
        self.exchange_name = 'apexpro'
        from apexpro.constants import APEX_HTTP_MAIN
        from apexpro.http_private_stark_key_sign import HttpPrivateStark
        from apexpro.http_private import HttpPrivate
        from apexpro.http_public import HttpPublic
        self.exchange = HttpPublic(APEX_HTTP_MAIN)
        self.exchange_account = HttpPrivateStark(
            endpoint=APEX_HTTP_MAIN,
            stark_public_key=self._api_config[account]['stark_public_key'],
            stark_private_key=self._api_config[account]['stark_private_key'],
            stark_public_key_y_coordinate=self._api_config[account]['stark_public_key_y_coordinate'],
            api_key_credentials=self._api_config[account]['api_key_credentials']
        )
        self.exchange_account.get_user()
        self.account_configs = self.exchange_account.configs_v2()
        self.exchange_account.get_account_v2()
        self.limit_fee = '0.0005'
        self.account_id = self.exchange_account.account['wallets'][0]['accountId']

    # region =============================================u swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.ticker(symbol=symbol.upper() + '-USDT')['data'][0]['lastPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.depth(symbol=symbol.upper() + '-USDT')['data']['b'][0][0])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.depth(symbol=symbol.upper() + '-USDT')['data']['a'][0][0])
        except:
            # print(format_exc())
            return None

    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['59736.8', '2.737'], 'ask': ['59737.2', '1.967']}
        """
        raw_orderbook = self.exchange.depth(
            symbol=symbol.upper() + '-USDT', limit=1)['data']
        return {
            'bid': raw_orderbook['b'][0],
            'ask': raw_orderbook['a'][0]
        }

    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            raw_orderbook = self.exchange.depth(
                symbol=symbol.upper() + '-USDT', limit=limit)['data']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['b'],
                'asks': raw_orderbook['a']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.ticker(symbol=symbol.upper() + '-USDT')['data'][0]['fundingRate'])
        except:
            print(format_exc())
            return None

        # 获取永续合约历史资金费信息

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大24(24小时)，修改下面day=1可获取更多
        :return: list越往后时间越靠近现在

        """
        try:
            funding_rates = self.exchange.history_funding_v2(
                symbol=symbol.upper() + '-USDT', limit=limit)['data']['historyFunds']
            return {
                'symbol': symbol,
                'funding_rate': [i['rate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

        # 获取永续合约持仓量

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            meta_info = self.exchange.ticker(
                symbol=symbol.upper() + '-USDT')['data'][0]
            return float(meta_info['openInterest']) * float(meta_info['lastPrice'])
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
          "baselinePositionValue": "50000.0000",
          "crossId": 30001,
          "crossSymbolId": 34,
          "crossSymbolName": "BTCUSDT",
          "digitMerge": "0.1,0.2,0.4,1,2",
          "displayMaxLeverage": "50",
          "displayMinLeverage": "1",
          "enableDisplay": true,
          "enableOpenPosition": true,
          "enableTrade": true,
          "fundingImpactMarginNotional": "10",
          "fundingInterestRate": "0.0003",
          "incrementalInitialMarginRate": "0.0075",
          "incrementalMaintenanceMarginRate": "0.005",
          "incrementalPositionValue": "150000.0000",
          "initialMarginRate": "0.02",
          "maintenanceMarginRate": "0.01",
          "maxOrderSize": "50",
          "maxPositionSize": "60",
          "minOrderSize": "0.0010",
          "maxMarketPriceRange": "0.025",
          "settleCurrencyId": "USDT",
          "starkExOraclePriceQuorum": "3",
          "starkExResolution": "10000000000",
          "starkExRiskFactor": "1417339208",
          "starkExSyntheticAssetId": "0x4254432d3130000000000000000000",
          "stepSize": "0.001",
          "symbol": "BTC-USDT",
          "symbolDisplayName": "BTCUSDT",
          "symbolDisplayName2": "BTCUSDT",
          "tickSize": "0.1",
          "underlyingCurrencyId": "BTC",
          "maxMaintenanceMarginRate": "0.3300",
          "maxPositionValue": "9650000",
          "tagIconUrl": "",
          "tag": "",
          "riskTip": false,
          "defaultLeverage": "10",
          "defaultInitialMarginRate": "",
          "klineStartTime": *************,
          "maxMarketSizeBuffer": "0.98"
        }
        """
        _info = self.account_configs['data']['usdtConfig']['perpetualContract']
        for i in _info:
            if i['crossSymbolName'] == symbol.upper() + 'USDT':
                return i

        # 获取下单价格精度

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['tickSize']), 10), 0))

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        {
            baselinePositionValue: "50000.0000",
            crossId: 30001,
            crossSymbolId: 40,
            crossSymbolName: "STXUSDT",
            digitMerge: "0.0001,0.0002,0.0004,0.001,0.002",
            displayMaxLeverage: "25",
            displayMinLeverage: "1",
            enableDisplay: True,
            enableOpenPosition: True,
            enableTrade: True,
            fundingImpactMarginNotional: "200",
            fundingInterestRate: "0.0003",
            incrementalInitialMarginRate: "0.01",
            incrementalMaintenanceMarginRate: "0.005",
            incrementalPositionValue: "50000.0000",
            initialMarginRate: "0.04",
            maintenanceMarginRate: "0.02",
            maxOrderSize: "5000",
            maxPositionSize: "20000",
            minOrderSize: "1",
            maxMarketPriceRange: "0.045",
            settleCurrencyId: "USDT",
            starkExOraclePriceQuorum: "3",
            starkExResolution: "1000000",
            starkExRiskFactor: "493921240",
            starkExSyntheticAssetId: "0x5354582d3600000000000000000000",
            stepSize: "1",
            symbol: "STX-USDT",
            symbolDisplayName: "STXUSDT",
            symbolDisplayName2: "STXUSDT",
            tickSize: "0.0001",
            underlyingCurrencyId: "STX",
            maxMaintenanceMarginRate: "0.1150",
            maxPositionValue: "1000000",
            tagIconUrl: "",
            tag: "",
            riskTip: False,
            defaultLeverage: "10",
            defaultInitialMarginRate: "",
            klineStartTime: 0,
            maxMarketSizeBuffer: "0.98",
            pullOffTime: **********
            }
        """
        return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['stepSize']), 10), 0))

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.account_configs['data']['usdtConfig']['perpetualContract']
            for i in _info:
                if i['crossSymbolName'] == symbol.upper() + 'USDT':
                    return float(i['minOrderSize'])
        except:
            print(format_exc())
            return None
        
    # 获取永续合约交易对(apexpro只有usdt合约)
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :return:
        """
        _symbols = []
        _info = [i['crossSymbolName'].lower() for i in self.account_configs['data']['usdtConfig']['perpetualContract'] if i['enableOpenPosition']]
        for i in _info:
            _symbol = i.replace('usdt', '')
            try:
                _price = self.get_swap_sell1(_symbol)
                _price_tick_size = self.get_swap_order_price_tick_size(_symbol)
                _amount_tick_size = self.get_swap_order_amount_tick_size(_symbol)
                _amount = round(100 / _price, _amount_tick_size)
                _order_info = self.place_swap_order(_symbol, 'buy', _amount, round(_price * 0.99, _price_tick_size), 'limit')
                if _order_info['order_id']:
                    cancel_order = self.cancel_swap_order(_symbol, _order_info['order_id'])
                    if cancel_order['status'] == 'canceled':
                        _symbols.append(i)
            except:
                pass
        return _symbols

        # 获取永续合约账户信息(单一币种,默认usdc)

    # 获取永续合约账户信息(单一币种,默认usdc)
    def get_swap_account_single_asset(self, symbol=None):
        try:
            return float(self.exchange_account.get_account_v2()['data']['wallets'][1]['balance'])
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
            {
                usdtBalance: {
                    totalEquityValue: "1018.417690348045200109481811523437500",
                    availableBalance: "1007.186191",
                    initialMargin: "11.23149846607819199562072753906250000",
                    maintenanceMargin: "5.61574923303909599781036376953125000",
                    walletBalance: "",
                    realizedPnl: "",
                    unrealizedPnl: ""
                },
                usdcBalance: {
                    totalEquityValue: "0.000000000000000000000000000000000",
                    availableBalance: "0.000000",
                    initialMargin: "0.00000000000000000000000000000000000000",
                    maintenanceMargin: "0.00000000000000000000000000000000000000",
                    walletBalance: "",
                    realizedPnl: "",
                    unrealizedPnl: ""
                }
            }
        """
        try:
            _info = self.exchange_account.get_account_balance_v2()['data']
            _margin_rate = float(_info['usdtBalance']['maintenanceMargin']) / float(_info['usdtBalance']['totalEquityValue'])
            return _margin_rate
        except:
            print(format_exc())
            return None
        
    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        """
        try:
            positions = []
            positions_info = self.exchange_account.get_account_v2()['data']['positions']
            for i in positions_info:
                if 'USDT' in i['symbol'] and float(i['size']) > 0:
                    positions.append(i)
            if symbol:
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if i['side'] == 'LONG' else 'sell',
                        'amount': i['size'],
                        'price': i['entryPrice'],
                        'liquidation_price': '',
                    } for i in positions if i['symbol'] == symbol.upper() + '-USDT'
                ]
            else:
                return [
                    {
                        'symbol': i['symbol'].replace('-USDT', '').lower(),
                        'direction': 'buy' if i['side'] == 'LONG' else 'sell',
                        'amount': i['size'],
                        'price': i['entryPrice'],
                        'liquidation_price': '',
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
          {
            "id": "611022329355436294",
            "clientId": "****************",
            "clientOrderId": "****************",
            "accountId": "515801624547951110",
            "symbol": "BTC-USDT",
            "side": "BUY",
            "price": "58000.0",
            "averagePrice": "",
            "limitFee": "0.027550",
            "fee": "",
            "liquidateFee": "",
            "triggerPrice": "0.0",
            "size": "0.001",
            "type": "LIMIT",
            "createdAt": *************,
            "updatedTime": *************,
            "expiresAt": *************,
            "status": "OPEN",
            "timeInForce": "GOOD_TIL_CANCEL",
            "reduceOnly": false,
            "isPositionTpsl": false,
            "orderId": "611022329355436294",
            "exitType": "",
            "latestMatchFillPrice": "0.0",
            "cumMatchFillSize": "0.000",
            "cumMatchFillValue": "0.0000",
            "cumMatchFillFee": "0.000000",
            "cumSuccessFillSize": "0.000",
            "cumSuccessFillValue": "0.0000",
            "cumSuccessFillFee": "0.000000",
            "triggerPriceType": "MARKET",
            "isOpenTpslOrder": false,
            "isSetOpenTp": false,
            "isSetOpenSl": false,
            "openTpParam": {},
            "openSlParam": {}
          }
        """
        try:
            open_order = self.exchange_account.open_orders_v2(token='USDT')[
                'data']
            if symbol:
                return [
                    {
                        'order_id': i['id'],
                        'symbol': symbol,
                        'direction': 'buy' if i['side'] == 'BUY' else 'sell',
                        'order_type': i['type'].lower(),
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': '',
                        'remain_amount': float(i['size']) - float(i['cumMatchFillSize']),
                    } for i in open_order if i['symbol'] == symbol.upper() + '-USDT'
                ]
            else:
                return [
                    {
                        'order_id': i['id'],
                        'symbol': i['symbol'].replace('-USDT', '').lower(),
                        'direction': 'buy' if i['side'] == 'BUY' else 'sell',
                        'order_type': i['type'].lower(),
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': '',
                        'remain_amount': float(i['size']) - float(i['cumMatchFillSize']),
                    } for i in open_order
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            open_order = self.get_swap_open_order(symbol)[0]
            return open_order['order_id']
        except:
            print(format_exc())
            return None

    # 下单 TODO：暂时没有市价单
    def place_swap_order(self, symbol, direction, amount, price=str, order_type='limit', close_position=False):
        try:
            currentTime = time()
            if order_type == 'limit':
                if not close_position:
                    order_info = self.exchange_account.create_order_v2(symbol=symbol.upper() + "-USDT", side=direction.upper(), type="LIMIT", size=str(
                        amount), price=str(price), limitFee=self.limit_fee, accountId=self.account_id, expirationEpochSeconds=currentTime)['data']
                else:
                    order_info = self.exchange_account.create_order_v2(symbol=symbol.upper() + "-USDT", side=direction.upper(), type="LIMIT", size=str(amount), 
                                                                       price=str(price), limitFee=self.limit_fee, accountId=self.account_id, reduceOnly=True, expirationEpochSeconds=currentTime)['data']
            # TODO:暂时没有市价单
            elif order_type == 'market':
                # 自动市价平仓
                if not close_position:
                    order_info = self.exchange_account.create_order_v2(symbol=symbol.upper() + "-USDT", side=direction.upper(
                    ), type="MARKET", size=str(amount), limitFee=self.limit_fee, accountId=self.account_id, expirationEpochSeconds=currentTime)['data']
                else:
                    order_info = self.exchange_account.create_order_v2(symbol=symbol.upper() + "-USDT", side=direction.upper(), type="MARKET", size=str(amount), limitFee=self.limit_fee, accountId=self.account_id, reduceOnly=True,
                                                                       expirationEpochSeconds=currentTime)['data']
            else:
                order_info = {'data': None}
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price,
                'order_type': order_type,
            }
        except:
            print(format_exc())
        return None

    def get_swap_order_info(self, symbol, order_id):
        try:
            order_info = self.exchange_account.get_order_v2(token='USDT', id=order_id)['data']
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol,
                'direction': 'buy' if order_info['side'] == 'BUT' else 'sell',
                'order_type': order_info['type'].lower(),
                'amount': order_info['size'],
                'price': order_info['price'],
                'average_price': float(order_info['cumSuccessFillValue']) / float(order_info['cumSuccessFillSize']),
                'remain_amount': float(order_info['size']) - float(order_info['cumMatchFillSize']),
                'fee': order_info['cumSuccessFillFee'],
            }
        except:
            print(format_exc())
            return None

    def cancel_swap_order(self, symbol, order_id):
        try:
            order_info = self.exchange_account.delete_order_v2(
                id=order_id, token='USDT')
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if 'detail' not in str(order_info) else 'failed'
            }
        except:
            print(format_exc())
            return None

        # endregion

