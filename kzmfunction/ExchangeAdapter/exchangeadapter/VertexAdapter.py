"""
备注说明：
    https://github.com/vertex-protocol/vertex-python-sdk
    pip install vertex-protocol
    version: 1.1.24
    
    
"""
# region ===============================================import
from .ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .api_config import vertex_api_config
from math import log
from time import time
# endregion ============================================import


class VertexAdapter(ExchangeAdapter):
    def __init__(self, account='vertex-持仓资金费'):
        self._api_config = vertex_api_config.api_config
        self.account = account
        self.exchange_name = 'vertex'

        from vertex_protocol.client import create_vertex_client, VertexClientMode
        from vertex_protocol.engine_client.types.execute import (
            OrderParams,
            # PlaceOrderParams,
            PlaceMarketOrderParams,
            MarketOrderParams,
            # WithdrawCollateralParams,
            CancelOrdersParams,
        )
        from vertex_protocol.utils.expiration import OrderType, get_expiration_timestamp
        from vertex_protocol.utils.nonce import gen_order_nonce
        from vertex_protocol.utils.subaccount import SubaccountParams
        from vertex_protocol.utils.math import to_pow_10, to_x18
        from vertex_protocol.utils import from_x18

        from eth_account import Account
        # # 创建public交易所
        self.arbitrum_address_key = self._api_config[account]['arbitrum_address_key']
        self.signer = Account.from_key(self.arbitrum_address_key)
        self.exchange = create_vertex_client(
            mode=VertexClientMode.MAINNET,
            signer=self.signer
        )
        self.owner = self.exchange.context.engine_client.signer.address
        self.sender = self.exchange.context.engine_client.signer.address + \
            '64656661756c74*********0'
        self.ethereum_address = self._api_config[account]['arbitrum_address']
        self.CancelOrdersParams = CancelOrdersParams
        self.OrderType = OrderType
        self.get_expiration_timestamp = get_expiration_timestamp
        self.gen_order_nonce = gen_order_nonce
        self.SubaccountParams = SubaccountParams
        self.OrderParams = OrderParams
        self.PlaceMarketOrderParams = PlaceMarketOrderParams
        self.MarketOrderParams = MarketOrderParams
        self.to_pow_10 = to_pow_10
        self.to_x18 = to_x18
        self.from_x18 = from_x18
        self.id_dict = self.get_symbol_id_dict()

    # region =======================================u swap
    # 获取永续合约币对的pruduct_id
    def get_symbol_id_dict(self):
        """
        :return:
        """
        # 获取product_id
        symbol_list = self.get_swap_instruments_symbols()
        symbol_info = [i[:-4] for i in symbol_list]
        id_dict = {}
        for i in symbol_info:
            id_dict[i] = self.get_symbol_product_id(i)
        return id_dict

    # 获取永续合约币对的pruduct_id
    def get_symbol_product_id(self, symbol):
        """
        :param symbol:
        :return:
        """
        return int(self.exchange.context.engine_client.get_symbols().symbols[f'{symbol.upper()}-PERP'].product_id)

    # TODO：获取最新价格
    def get_swap_latest_price(self, symbol):
        pass

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            _id = self.id_dict[symbol]
            return float(self.exchange.context.engine_client.get_market_price(_id).bid_x18) / 10 ** 18
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            _id = self.id_dict[symbol]
            return float(self.exchange.context.engine_client.get_market_price(_id).ask_x18) / 10 ** 18
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60972.0', '0.85664'], 'ask': ['60973.0', '0.65602']}
        """
        _id = self.id_dict[symbol]
        raw_orderbook = self.exchange.market.get_market_liquidity(_id, 1)
        return {
            'bid': [float(raw_orderbook.bids[0][0]) / 10 ** 18, float(raw_orderbook.bids[0][0]) / 10 ** 18],
            'ask': [float(raw_orderbook.asks[0][0]) / 10 ** 18, float(raw_orderbook.asks[0][0]) / 10 ** 18],
        }

    # 获取swap盘口
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            _id = self.id_dict[symbol]
            raw_orderbook = self.exchange.market.get_market_liquidity(
                _id, limit)
            return {
                'symbol': symbol,
                'bids': [[float(i[0]) / 10 ** 18, float(i[1]) / 10 ** 18] for i in raw_orderbook.bids],
                'asks': [[float(i[0]) / 10 ** 18, float(i[1]) / 10 ** 18] for i in raw_orderbook.asks],
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            _id = self.id_dict[symbol]
            return float((self.exchange.market.get_perp_funding_rate(_id).funding_rate_x18)) / 10 ** 18 / 24
        except:
            print(format_exc())
            return None

        # 获取永续合约历史资金费信息

    # TODO：获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大24(24小时)，修改下面day=1可获取更多
        :return: list越往后时间越靠近现在

        """
        pass

    # 获取永续合约持仓量(usdc)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        info = self.exchange.context.engine_client.get_all_products().perp_products
        _id = self.id_dict[symbol]
        for i in list(info):
            if dict(i)['product_id'] == _id:
                _price = self.get_swap_buy1(symbol)
                return int(dict(dict(i)['state'])['open_interest']) / 10 ** 18 * _price
        return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
            type='perp'
            product_id='6'
            symbol='ARB-PERP'
            price_increment_x18='1*********00000'
            size_increment='1******************0'
            min_size='25******************0'
            min_depth_x18='25******************00'
            max_spread_rate_x18='5*********000000'
            maker_fee_rate_x18='0'
            taker_fee_rate_x18='3*********00000'
            long_weight_initial_x18='9*********00000000'
            long_weight_maintenance_x18='95*********0000000'
        """
        return self.exchange.context.engine_client.get_symbols().symbols[f'{symbol.upper()}-PERP']

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        return int(round(log(1 / (int(self.exchange.context.engine_client.get_symbols().symbols[f'{symbol.upper()}-PERP'].price_increment_x18) / (10 ** 18)), 10), 0))

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        return max(int(round(log(1 / (int(self.exchange.context.engine_client.get_symbols().symbols[f'{symbol.upper()}-PERP'].size_increment) / (10 ** 18)), 10), 0)), 0)

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            return float(self.exchange.context.engine_client.get_symbols().symbols[f'{symbol.upper()}-PERP'].min_size) / (10 ** 18)
        except:
            print(format_exc())
            return None

    # TODO：获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
        """
        try:
            pass
        except:
            print(format_exc())
            return None

    # 获取永续合约交易对(vertex只有usdt合约)
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        添加usdt是为了后续和其他交易所做比对，实际应该为usdc合约
        :return:
        """
        try:
            return [i.symbol.replace('-PERP', 'usdt').lower() for i in self.exchange.context.engine_client.get_product_symbols() if i.symbol.endswith('PERP')]
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdc)
    def get_swap_account_single_asset(self, symbol='usdc'):
        try:
            return float(dict(dict(self.exchange.context.engine_client.get_subaccount_info(self.sender))['healths'][0])['assets']) / 10 ** 18
        except:
            print(format_exc())
            return None

    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        """
        def cal_liquidation_price(_position, _perp):
            balance_amount = int(_position.balance.amount) / 10**18
            liq_price = int(_perp.oracle_price_x18) / 10**18

            if balance_amount > 0:
                long_weight = int(_perp.risk.long_weight_maintenance_x18) / 10**18
                liq_price -= maint_health / balance_amount / long_weight
            else:
                short_weight = int(_perp.risk.short_weight_maintenance_x18) / 10**18
                liq_price += maint_health / abs(balance_amount) * short_weight
            return liq_price
        
        try:
            subaccount_summary = self.exchange.context.engine_client.get_subaccount_info(self.sender)
            maint_health = int(subaccount_summary.healths[1].health) / 10**18
            if symbol:
                _id = self.id_dict[symbol]
                _positions = next((i for i in subaccount_summary.perp_balances if i.product_id == _id), None)
                _perp = next((i for i in subaccount_summary.perp_products if i.product_id == _id), None)
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(_positions.balance.amount) > 0 else 'sell',
                        'amount': abs(float(_positions.balance.amount)) / 10 ** 18,
                        'price': '',
                        'liquidation_price': cal_liquidation_price(_positions, _perp),
                    }
                ]
            else:
                perp_balances = subaccount_summary.perp_balances
                perp_products = subaccount_summary.perp_products
                _position_id = [i.product_id for i in perp_balances if i.balance.amount != '0']
                _positions = {}
                for i in _position_id:
                    _positions[i] = [j for j in perp_balances if j.product_id == i] + [k for k in perp_products if k.product_id == i]
                return [
                    {
                        'symbol': next((key for key, val in self.id_dict.items() if val == i), None),
                        'direction': 'buy' if float(_positions[i][0].balance.amount) > 0 else 'sell',
                        'amount': abs(float(_positions[i][0].balance.amount)) / 10 ** 18,
                        'price': '',
                        'liquidation_price': cal_liquidation_price(_positions[i][0], _positions[i][1]),
                    } for i in _positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        {
          "product_id": 4,
          "sender": "0x55d04fab119ac3cc5b0e9421bcc3c4ed9af4b80d64656661756c74*********0",
          "price_x18": "25******************00",
          "amount": "1*********00000000",
          "expiration": "*************",
          "nonce": "1808541696865075459",
          "unfilled_amount": "1*********00000000",
          "digest": "0xe1a9a16da419c3c587ccb0df08552c7c090ce75b92b03417dd757a7c98a61220",
          "placed_at": "**********"
        }
        """
        try:
            _id = self.id_dict[symbol]
            open_order = list(dict(i) for i in self.exchange.market.get_subaccount_open_orders(
                product_id=4, sender=self.sender).orders)
            if symbol:
                return [
                    {
                        'order_id': i['digest'],
                        'symbol': symbol,
                        'direction': 'buy' if int(i['amount']) > 0 else 'sell',
                        'order_type': '',
                        'amount': abs(int(i['amount']) / 10 ** 18),
                        'price': int(i['price_x18']) / 10 ** 18,
                        'average_price': '',
                        'remain_amount': '',
                    } for i in open_order if i['product_id'] == _id
                ]
            else:
                return [
                    {
                        'order_id': i['digest'],
                        'symbol': symbol,
                        'direction': 'buy' if int(i['amount']) > 0 else 'sell',
                        'order_type': '',
                        'amount': abs(int(i['amount']) / 10 ** 18),
                        'price': int(i['price_x18']) / 10 ** 18,
                        'average_price': '',
                        'remain_amount': '',
                    } for i in open_order if i['product_id'] == _id
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            open_order = self.get_swap_open_order(symbol)[0]
            return open_order['order_id']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
            {
              "status": "success",
              "signature": "0xe0e974f7a6467721cec286d7d8782e2c8dae266d5fa99f69d5f6f55bd316a5db7dac03a79d5d7f7bf341bcec94d62e80e67a22bec8b1613a53a28c063bac6c801c",
              "data": {
                "digest": "0x35d3eeaa3efe31ff711343ff416010d2f9867828fa161b3572eef4cbb6da0243"
              },
              "request_type": "execute_place_order",
              "req": {
                "place_order": {
                  "signature": "0xe0e974f7a6467721cec286d7d8782e2c8dae266d5fa99f69d5f6f55bd316a5db7dac03a79d5d7f7bf341bcec94d62e80e67a22bec8b1613a53a28c063bac6c801c",
                  "product_id": 36,
                  "order": {
                    "sender": "0xfd1934bb734b85a7f7bc1b210fa325f6627b39bb64656661756c74*********0",
                    "nonce": "1783273303482303362",
                    "amount": "3******************0",
                    "priceX18": "12******************",
                    "expiration": "13835058056982825610"
                  }
                }
              }
            }
        """
        try:
            _id = self.id_dict[symbol]
            unix_epoch = int(time())
            if direction == 'sell':
                _amount = -amount
            else:
                _amount = amount
            if order_type == 'limit':
                order = self.OrderParams(
                    sender=self.SubaccountParams(
                        subaccount_owner=self.owner,
                        subaccount_name="default",
                    ),
                    priceX18=self.to_x18(price),
                    amount=self.to_pow_10(_amount, 18),
                    expiration=self.get_expiration_timestamp(
                        self.OrderType.DEFAULT, int(time()) + 9999999),
                    nonce=self.gen_order_nonce(),
                )
                if close_position:
                    order.expiration = str(
                        (unix_epoch + 9999) | (1 << 62) | (1 << 61))
                order_info = self.exchange.market.place_order(
                    {"product_id": _id, "order": order})
            elif order_type == 'market':
                order = self.PlaceMarketOrderParams(
                    product_id=_id,
                    market_order=self.MarketOrderParams(
                        sender=self.SubaccountParams(
                            subaccount_owner=self.owner,
                            subaccount_name="default",
                        ),
                        nonce=self.gen_order_nonce(),
                        amount=self.to_pow_10(_amount, 18)
                    )
                )
                order_info = self.exchange.market.place_market_order(order)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': dict(order_info.data)['digest'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取swap订单详情
    def get_swap_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
          "product_id": 4,
          "sender": "0x55d04fab119ac3cc5b0e9421bcc3c4ed9af4b80d64656661756c74*********0",
          "price_x18": "25******************00",
          "amount": "1*********00000000",
          "expiration": "1724766878865",
          "nonce": "1808549246087987564",
          "unfilled_amount": "1*********00000000",
          "digest": "0x186fc149a7fc8de320fe39e68d3187ee7aaba2c44dfdedbb893d0255df45449d",
          "placed_at": "1724766881"
        }
        """
        try:
            _id = self.id_dict[symbol]
            order_info = dict(
                self.exchange.context.engine_client.get_order(_id, order_id))
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': symbol,
                'direction': 'buy' if int(order_info['amount']) > 0 else 'sell',
                'order_type': '',
                'amount': order_info['amount'],
                'price': order_info['price_x18'],
                'average_price': '',
                'remain_amount': order_info['unfilled_amount'],
                'fee': ''
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        """
        _id = self.id_dict[symbol]
        try:
            _id = self.id_dict[symbol]
            owner = self.exchange.context.engine_client.signer.address
            cancel_order = self.CancelOrdersParams(
                sender=self.SubaccountParams(
                    subaccount_owner=owner,
                    subaccount_name="default",
                ),
                productIds=[_id],
                digests=[order_id],
                nonce=self.gen_order_nonce()
            )
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if dict(self.exchange.market.cancel_orders(cancel_order).data)['cancelled_orders'] else 'failed'
            }
        except:
            print(format_exc())
            return None
    # endregion
