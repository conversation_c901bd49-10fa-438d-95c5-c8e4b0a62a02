# region ===============================================描述
"""
    套利资金费，卖出现货，买入合约。
    版本区别：根据盘口数量下单，而非v1版本的固定数量下单
"""
# endregion ============================================描述


# region ===============================================import
from math import ceil
from threading import Thread
from time import sleep

from kzmfunction.ExchangeAdapter.exchangeadapter.BinanceAdapter import BinanceAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BybitAdapter import BybitAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.VertexAdapter import VertexAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.ApexproAdapter import ApexproAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.GateioAdapter import GateioAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.HyperliquidAdapter import HyperliquidAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BitgetAdapter import BitgetAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.OkxAdapter import OkxAdapter
from kzmfunction.function import sendSlackMsgAsync
import sys
from traceback import format_exc
# endregion ============================================import


# region ===============================================函数
# 获取exchange_1,exchange_2报价情况
def get_price():
    """
    获取exchange_1,exchange_2报价情况
    :return:
    """

    # 获取exchange_1报价
    def get_exchange_1_orderbook():
        """
        获取现货报价
        """
        _orderbook['exchange_1_orderbook'] = exchange_1.get_spot_best_orderbook(
            symbol)

    # 获取exchange_2报价
    def get_exchange_2_orderbook():
        """
        获取exchange_1报价
        """
        _orderbook['exchange_2_orderbook'] = exchange_2.get_swap_best_orderbook(
            symbol)

    _orderbook = {}
    n = 0
    for _ in range(3):
        try:
            # 获取exchange_1报价
            exchange_1_price_thread = Thread(
                target=get_exchange_1_orderbook, args=[])
            # 获取exchange_2报价
            exchange_2_price_thread = Thread(
                target=get_exchange_2_orderbook, args=[])

            # start
            exchange_1_price_thread.start()
            exchange_2_price_thread.start()

            # wait
            exchange_1_price_thread.join()
            exchange_2_price_thread.join()

            _exchange_1_orderbook = _orderbook.get(
                'exchange_1_orderbook', None)
            _exchange_2_orderbook = _orderbook.get(
                'exchange_2_orderbook', None)
        except Exception as _e:
            n += 1
            if n > 2:
                return None, None
            print(_e)
        else:
            if _exchange_1_orderbook and _exchange_2_orderbook:
                return _exchange_1_orderbook, _exchange_2_orderbook
            else:
                return None, None


# exchange_1/exchange_2下单
def place_order():
    """
    exchange_1/exchange_2下单
    :return:
    """

    # exchange_1下单卖出
    def place_exchange_1_order():
        """
        exchange_1下单
        """
        _order['exchange_1_order'] = exchange_1.place_spot_order(
            symbol=symbol, direction='sell', order_type='limit', amount=execute_amount, price=exchange_1_price)

    # exchange_2下单买入
    def place_exchange_2_order():
        """
        exchange_2下单
        """
        if close_position:
            _order['exchange_2_order'] = exchange_2.place_swap_order(
                symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price)
        else:
            _order['exchange_2_order'] = exchange_2.place_swap_order(
                symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price)

    _order = {}
    n = 0
    for _ in range(3):
        try:
            # 获取exchange_1单
            exchange_1_order_thread = Thread(
                target=place_exchange_1_order, args=[])
            # 获取exchange_2单
            exchange_2_order_thread = Thread(
                target=place_exchange_2_order, args=[])

            # start
            exchange_1_order_thread.start()
            exchange_2_order_thread.start()

            # wait
            exchange_1_order_thread.join()
            exchange_2_order_thread.join()

            _exchange_1_order = _order.get('exchange_1_order', None)
            _exchange_2_order = _order.get('exchange_2_order', None)
        except Exception as _e:
            n += 1
            if n > 2:
                return None, None
            print(_e)
        else:
            if _exchange_1_order and _exchange_2_order:
                return _exchange_1_order, _exchange_2_order
            else:
                print(_exchange_1_order)
                print(_exchange_2_order)
                return None, None


# endregion ============================================函数


# region ===============================================设置
symbol = 'ena'
# 卖出现货交易所：
exchange_1_name = 'binance'
# exchange_1_name = 'bitget'
# exchange_1_name = 'gateio'
# exchange_1_name = 'hyperliquid-套利账号'
# 买入合约交易所：
exchange_2_name = 'hyperliquid'
# exchange_2_name = 'bybit'
# exchange_2_name = 'bitget'
# exchange_2_name = 'binance'
exchange_account = {'binance': '<EMAIL>', 'gateio': '<EMAIL>', 
                     'bitget': '<EMAIL>', 'okx': '<EMAIL>',
                     'hyperliquid': 'hyperliquid-持仓账号', 'vertex': 'vertex-持仓资金费',
                     'bybit': '<EMAIL>', 'hyperliquid-套利账号': 'hyperliquid-套利账号'}
exchange_dict = {'binance': BinanceAdapter, 'gateio': GateioAdapter, 'hyperliquid': HyperliquidAdapter,
                 'bitget': BitgetAdapter, # 'vertex': VertexAdapter, 'apexpro': ApexproAdapter,
                 'bybit': BybitAdapter, 'okx': OkxAdapter, 'hyperliquid-套利账号': HyperliquidAdapter}
exchange_1 = exchange_dict[exchange_1_name](exchange_account[exchange_1_name])
exchange_2 = exchange_dict[exchange_2_name](exchange_account[exchange_2_name])
exchange1_name, exchange_2_name = exchange_1.exchange_name, exchange_2.exchange_name
r_threshold = 0.0003
close_position = True
symbol_price = exchange_1.get_spot_buy1(symbol)
total_amount = 0  # 总交易数量
exchange_1_price_precision = exchange_1.get_spot_order_price_tick_size(symbol)
exchange_1_amount_precision = exchange_1.get_spot_order_amount_tick_size(symbol)
exchange_2_price_precision = exchange_2.get_swap_order_price_tick_size(symbol)
exchange_2_amount_precision = exchange_2.get_swap_order_amount_tick_size(symbol)
amount_precision = min(exchange_1_amount_precision,
                       exchange_2_amount_precision)
min_execute_amount = round(10 * 1.3 / symbol_price,
                           amount_precision) / 0.8  # 盘口最低币数量要求
min_trade_amount = ceil(min_execute_amount * 1.5 * 10 **
                        exchange_2_price_precision) / 10 ** exchange_2_price_precision
report_title = f'{exchange1_name}/{exchange_2_name}资金费套利程序'
exchange_1_max_execute_num = exchange_1.get_spot_account_single_asset(symbol)
# endregion ============================================设置


# region ===============================================主程序
if __name__ == '__main__':
    try:
        # TODO：数量逻辑上存在bug？
        if close_position:
            # 查看持仓
            exchange_1_max_execute_num = exchange_1.get_spot_account_single_asset(symbol)
            exchange_2_max_execute_num = float(exchange_2.get_swap_position(symbol)[0]['amount'])
            if all([exchange_1_max_execute_num, exchange_2_max_execute_num]):
                max_execute_num = max(exchange_1_max_execute_num, exchange_2_max_execute_num) if exchange_1_max_execute_num == 0 else min(
                    exchange_2_max_execute_num, exchange_1_max_execute_num)
                # max_execute_num = 4490
            else:
                print('当前账户没有持仓，退出程序！')
                sys.exit()
        else:
            # exchange_1_max_execute_num = exchange_1.get_spot_account_single_asset(symbol)
            exchange_1_max_execute_num = 1000000
            if exchange_1_max_execute_num > 0:
                max_execute_num = exchange_1_max_execute_num
                # max_execute_num = 800
            else:
                print(f'当前{exchange_1_name}账户没有持仓，退出程序！')
                sys.exit()
        print('当前账户持仓数量：', max_execute_num)
        while True:
            try:
                # 获取exchange_1和exchange_2盘口
                exchange_1_orderbook, exchange_2_orderbook = get_price()
                exchange_1_buy1_price = float(exchange_1_orderbook['bid'][0])
                exchange_1_buy1_amount = float(exchange_1_orderbook['bid'][1])
                exchange_2_sell1_price = float(exchange_2_orderbook['ask'][0])
                exchange_2_sell1_amount = float(exchange_2_orderbook['ask'][1])
            except Exception as e:
                print(format_exc())
                continue
            else:
                try:
                    # 计算价差
                    r = exchange_1_buy1_price / exchange_2_sell1_price - 1
                    # ===判断价差是否满足要求
                    # =====自动入金
                    if r < r_threshold:
                        print(f'{exchange1_name}现货价格:{exchange_1_buy1_price:.4f}，{exchange_2_name}合约价格:{exchange_2_sell1_price:.4f}，价差:{r:.4f}, {exchange1_name}数量:{exchange_1_buy1_amount:.2f}, {exchange_2_name}数量:{exchange_2_sell1_amount}.2f')
                        print('利差小于目标阀值，不入金')
                    else:
                        if exchange_1_buy1_amount > min_execute_amount and exchange_2_sell1_amount > min_execute_amount:
                            exchange_1_price = round(
                                exchange_1_buy1_price * 0.99, exchange_1_price_precision)
                            exchange_2_price = round(
                                exchange_2_sell1_price * 1.01, exchange_2_price_precision)
                            execute_amount = round(max(min(exchange_2_sell1_amount, exchange_1_buy1_amount,
                                                   max_execute_num - total_amount) * 0.8, min_trade_amount), amount_precision)
                            # execute_amount = round(execute_amount * 2) / 2
                            # if execute_amount % 2 != 0:
                            #     execute_amount += 1
                            order_info, order_info2 = place_order()
                            if not order_info['order_id'] or not order_info2['order_id']:
                                print('下单失败，请检查订单！！')
                                print(order_info)
                                print(order_info2)
                                sendSlackMsgAsync(
                                    content=f'{report_title}:\n{symbol}下单失败，请检查订单！！', channel='套利消息')
                                sys.exit()
                            print(f'{exchange_2_name}价格：%.4f，{exchange_1_name}价格：%.4f，价差：%.4f' % (
                                exchange_2_sell1_price, exchange_1_buy1_price, r))
                            print('利差和盘口数量大于目标阀值，开始入金')
                            print(
                                f'交易计划： {exchange_1_name}买入现货, 买入价格{exchange_1_buy1_price}, {exchange_2_name}卖出合约, 卖出价格{exchange_2_sell1_price}, 交易数量{execute_amount}')
                            print(order_info)
                            print(order_info2)
                            # 计算累计成交量
                            total_amount += execute_amount
                        else:
                            print(f'{exchange_2_name}价格：%.4f，{exchange_1_name}价格：%.4f，价差：%.4f' % (
                                exchange_2_sell1_price, exchange_1_buy1_price, r))
                            print(f'{exchange_2_name}盘口数量：',
                                  exchange_2_sell1_amount)
                            print(f'{exchange_1_name}盘口数量：',
                                  exchange_1_buy1_amount)
                            print('利差大于目标阀值，但盘口数量不足，不入金')
                    # ===循环结束
                    print('*' * 20, '本次循环结束，暂停', '*' * 20, '\n')
                    sleep(1)
                    if total_amount < max_execute_num:
                        if max_execute_num - total_amount < min_trade_amount:
                            print(
                                f'交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易')
                            sendSlackMsgAsync(
                                content=f'{report_title}:\n{symbol}交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易！', channel='套利消息')
                            break
                        print(
                            f'{symbol}进行下一轮循环, 已执行数量{total_amount}, 完成比例: {total_amount / max_execute_num * 100} %')
                    else:
                        print('达到最大下单币数，完成建仓计划，退出程序')
                        sendSlackMsgAsync(
                            content=f'{report_title}:\n{symbol}下单完成！', channel='套利消息')
                        break
                except Exception as e:
                    sendSlackMsgAsync(
                        f'{report_title}:\n{symbol}出现异常，退出程序！', channel='报错消息')
                    print(format_exc())
                    sys.exit()
    except:
        print(format_exc())
        sys.exit()
# endregion ============================================主程序
